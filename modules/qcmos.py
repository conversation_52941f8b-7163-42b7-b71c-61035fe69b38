from __future__ import annotations

import time
from copy import deepcopy

import numpy as np
from PIL import Image
from artiq.experiment import *
from modules.config import *
from modules.QCMOS_package.qcmos_driver import *
import cv2
from typing import Optional, Tuple
from typing import List, Union
from modules.ion_detector import *
from ionctrl_pkg.utils.log import get_logger

logger = get_logger(__name__)
def _optimal_threshold_1d(
        hist_dark: np.ndarray,
        hist_bright: np.ndarray,
) -> Tuple[int, float, float]:
    """
    根据一维直方图计算最优判决阈值。

    通过最小化暗态判亮和亮态判暗的错误概率之和来确定阈值。
    给定一个阈值 t，错误概率定义为：
    P(错误|暗态) = P(计数 > t | 暗态)
    P(错误|亮态) = P(计数 <= t | 亮态)
    目标是找到 t* 使得 P(错误|暗态) + P(错误|亮态) 最小。

    Parameters:
        hist_dark: 暗态计数的直方图 (numpy array)，数组索引代表计数值，数组值代表该计数值出现的频数。shppe=[N,1];
        hist_bright: 亮态计数的直方图 (numpy array)，格式同上。shppe=[N,1];


    Returns:
        Tuple[int, float, float]: 包含
            - t_star (int): 最优判决阈值。
            - p_dark_to_bright (float): 在最优阈值下，暗态被误判为亮态的概率 P(计数 > t* | 暗态)。
            - p_bright_to_dark (float): 在最优阈值下，亮态被误判为暗态的概率 P(计数 <= t* | 亮态)。

    Note:
        - 此方法假设输入的直方图能充分代表暗态和亮态的计数分布。
        - 寻找最优阈值的标准是最小化两种错误概率之和，这在许多场景下是合理的，
          但不一定是所有应用场景下的唯一或最佳标准。
        - 输入的两个直方图应该具有相同的 bin 边界和宽度。
    """
    # 计算暗态和亮态计数的累积分布函数 (Cumulative Distribution Function, CDF)
    # cdf_dark[i] 表示暗态计数 <= i 的总次数
    cdf_dark = np.cumsum(hist_dark)  # shape=[N, 1]
    # cdf_bright[i] 表示亮态计数 <= i 的总次数
    cdf_bright = np.cumsum(hist_bright)  # shape=[N, 1]
    # 获取暗态和亮态的总计数（总样本数）
    total_dark = cdf_dark[-1]  # 最后一个值确实是所有计数的和, 即总计数, tpye = int, shape=[1, ]
    total_bright = cdf_bright[-1]

    # 防止除零错误，如果总计数为0，则错误率为0
    if total_dark == 0:
        p_dark_to_bright = np.zeros_like(cdf_dark, dtype=float)
    else:
        # 对于每个可能的阈值 t (对应数组索引)，并行计算暗态被误判为亮态的概率
        # P(计数 > t | 暗态) = (总暗态数 - 暗态计数 <= t 的数量) / 总暗态数
        p_dark_to_bright = (total_dark - cdf_dark) / total_dark

    if total_bright == 0:
        p_bright_to_dark = np.zeros_like(cdf_bright, dtype=float)
    else:
        # 对于每个可能的阈值 t (对应数组索引)，计算亮态被误判为暗态的概率
        # P(计数 <= t | 亮态) = (亮态计数 <= t 的数量) / 总亮态数
        p_bright_to_dark = cdf_bright / total_bright

    # 计算总错误率，即两种错误概率之和
    p_error = p_dark_to_bright + p_bright_to_dark
    # 找到使总错误率最小的索引，该索引即为最优阈值 t*
    t_star = int(np.argmin(p_error))

    # 返回最优阈值 t* 以及在该阈值下的两种错误概率
    # 注意索引 t_star 对应的是计数阈值
    return (
        t_star,
        float(p_dark_to_bright[t_star]),
        float(p_bright_to_dark[t_star]),
    )

def kronecker_product(matrices, indices):
    """安全的Kronecker积计算，处理各种索引输入"""
    # 转换索引为整数数组
    indices = np.asarray(indices)
    if indices.dtype == bool:
        indices = np.where(indices)[0]  # 布尔掩码转整数索引

    # 确保索引是整数
    indices = indices.astype(int)

    # 选择矩阵并计算直积
    result = matrices[indices[0]]
    for i in indices[1:]:
        result = np.kron(result, matrices[i])
    return result

def process_for_histogram_per_ion(
        self,
        re_repeat: int = 1,
        bins: int | None = None,
) -> None:
    """
    为每个选定的离子计算独立的判决阈值，并写入 ARTIST 数据集。

    从缓存的图像数据中提取每个离子的计数，生成直方图，
    并调用 _optimal_threshold_1d 寻找最优阈值。

    Parameters
    ----------
    ion_choice : Sequence[int] | None, optional
        要分析的离子索引列表；若为 `None`，则分析所有离子。默认为 None。
    re_repeat : int, default 1
        等效的重复次数放大因子，总重复次数为 `Experiment.Repeat * re_repeat`。
    bins : int | None, optional
        生成直方图时的 bin 数量。若为 `None`，则根据每个离子的数据范围自动确定。默认为 None。
    """
    # ---------- 从缓存图像帧计算离子计数矩阵 ----------
    repeat_total = self.parameter.Experiment.Repeat * re_repeat
    # pump_img 对应暗态 (odd frames), ctrl_img 对应亮态 (even frames)
    pump_img, ctrl_img = self.odd_cache, self.even_cache

    # 使用 ROI 批量计算每个离子在每次重复中的像素总和
    pump_matrix = calculate_roi_pixel_sums_batch(
        pump_img,
        self.roi_for_ions,
        repeat=repeat_total,
        background_noise=self.parameter.QCMOS.bit16.background_noise,
    )
    ctrl_matrix = calculate_roi_pixel_sums_batch(
        ctrl_img,
        self.roi_for_ions,
        repeat=repeat_total,
        background_noise=self.parameter.QCMOS.bit16.background_noise,
    )

    # ----- 选择要处理的离子(选择所有离子) -----
    ion_choice = range(pump_matrix.shape[1])
    ion_choice = list(ion_choice)  # 转换为列表方便处理

    # 初始化用于存储结果的列表
    histograms: List[np.ndarray] = []  # 存储每个离子的直方图 (暗态, 亮态)
    thresholds: List[int] = []  # 存储每个离子的最优阈值
    errors_dark_to_bright: List[float] = []  # 存储每个离子的暗态判亮错误率
    errors_bright_to_dark: List[float] = []  # 存储每个离子的亮态判暗错误率

    # ---------- 遍历每个选定的离子 ----------
    for i in ion_choice:
        # 提取当前离子的暗态和亮态计数数据
        pump_counts = pump_matrix[:, i]
        ctrl_counts = ctrl_matrix[:, i]
        # 合并暗态和亮态数据以确定直方图的范围和 bin 数量
        all_counts = np.concatenate([pump_counts, ctrl_counts])

        # 确定直方图的 bin 数量：始终根据数据范围自动计算
        # 确保至少有一个 bin
        data_range = all_counts.max() - all_counts.min()
        nbins = int(data_range + 1) if data_range >= 0 else 1

        # 生成暗态直方图
        # 使用与 nbins 匹配的范围，确保边界正确
        hist_range = (all_counts.min(), all_counts.max() + 1)  # +1 因为 range 是 [min, max)
        hist_dark = np.histogram(
            pump_counts, bins=nbins, range=(all_counts.min(), all_counts.max())
        )[0]
        # 生成亮态直方图
        hist_bright = np.histogram(
            ctrl_counts, bins=nbins, range=(all_counts.min(), all_counts.max())
        )[0]
        # 将暗态和亮态直方图堆叠在一起
        hist_i = np.stack((hist_dark, hist_bright), axis=1)
        histograms.append(hist_i)

        # 计算当前离子的最优阈值和错误率
        t_star, p_d2b, p_b2d = _optimal_threshold_1d(hist_dark, hist_bright)
        thresholds.append(t_star)
        errors_dark_to_bright.append(p_d2b)
        errors_bright_to_dark.append(p_b2d)

    # ---------- 将结果写入 ARTIST 数据集 ----------
    # 保存所有选定离子的直方图数据
    self.set_dataset(
        "histograms", [h.tolist() for h in histograms], broadcast=True
    )
    # 保存所有选定离子的最优阈值
    self.set_dataset(
        "thresholds", thresholds, broadcast=True
    )
    # 保存所有选定离子的错误率 (结构化字典)
    self.set_dataset(
        "errors",
        {
            "dark_to_bright": errors_dark_to_bright,
            "bright_to_dark": errors_bright_to_dark,
        },
        broadcast=True,
    )


def pad_pixel_marks(pixel_marks: List[List[List[int]]]) -> np.ndarray:
    if not pixel_marks:  # 如果为空，返回空数组
        return np.array([], dtype=np.int32).reshape(0, 0, 2)  # 形状 (0, 0, 2)

    max_pixels = max(len(ion) for ion in pixel_marks)  # 找到最长的离子像素列表
    padded = []
    for ion in pixel_marks:
        padded_ion = ion + [[-1, -1]] * (max_pixels - len(ion))  # 用 [-1, -1] 填充
        padded.append(padded_ion)
    return np.array(padded, dtype=np.int32)  # 转换为均匀数组

def detect_brightest_pixels_from_avg(
        cooling_avg: np.ndarray,
        pumping_avg: np.ndarray,
        *,
        min_pixel_count: int = 1,
        brightness_threshold: float,
        max_candidate_pixels: int = 10,
        ion_exist_threshold: int = 213
) -> List[List[List[int]]]:
    """
    利用两张预平均 cooling/pumping 图像，通过亮度阈值检测每个离子的像素点标记。

    Parameters
    ----------
    ion_exist_threshold
    cooling_avg : np.ndarray
        处于冷却态（离子发光）的灰度平均图像，形状 ``[H, W]``。
    pumping_avg : np.ndarray
        处于抽运态（离子暗态）的灰度平均图像，与 ``cooling_avg`` 形状一致。
    min_pixel_count : int, optional
        连通域最小像素数阈值，默认值为 ``1``。
    brightness_threshold : float
        亮度阈值，从最亮的像素开始累加，直到达到此阈值的最小像素数。
    max_candidate_pixels : int, optional
        候选像素的最大数量，默认值为 ``10``。

    Returns
    -------
    List[List[List[int]]]
        像素标记列表，每个离子对应一个子列表，格式为 [[[x1, y1], [x2, y2], ...], ...]。
    """
    if cooling_avg.shape != pumping_avg.shape:
        raise ValueError("cooling_avg and pumping_avg must have same shape")
    if cooling_avg.ndim != 2:
        raise ValueError("image must be single channel picture")

    # 差分并归一化
    diff = cv2.subtract(cooling_avg, pumping_avg, dtype=cv2.CV_64F)  # 避免两种数据格式不一样产生报错, 直接指定输出格式
    diff_u8 = cv2.normalize(diff, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)

    # 自适应阈值分割
    T = diff_u8.mean() + 5.0 * diff_u8.std()
    _, mask = cv2.threshold(diff_u8, T, 255, cv2.THRESH_BINARY)

    # 连通域分析
    num_labels, labels, stats, _ = cv2.connectedComponentsWithStats(mask, 8)
    pixel_marks_for_ions: List[List[List[int]]] = []

    # logger.info(f"num_labels {num_labels}")
    for i in range(1, num_labels):  # 跳过背景标签 0
        area = int(stats[i, cv2.CC_STAT_AREA])
        # logger.info(f"area {i}: pixel_num = {area}")
        if area < min_pixel_count:
            continue
        # 找到该连通域中的所有像素位置
        comp_mask = (labels == i)

        # 从原始的 cooling_avg 图像中，提取该连通域对应位置的像素值
        cooling_values_in_region = cooling_avg[comp_mask]

        # 检查该区域在 cooling_avg 中是否有任何像素值大于 ion_exist_threshold
        # np.any() 会返回 True 或 False
        if not np.any(cooling_values_in_region > ion_exist_threshold):
            # 如果没有，则跳过这个区域，不认为是离子
            continue

        y_coords, x_coords = np.where(comp_mask)
        pixel_values = diff[comp_mask]

        # 按亮度从大到小排序
        sorted_indices = np.argsort(pixel_values)[::-1]

        # 基于亮度阈值的动态像素选择
        cumulative_brightness = 0.0
        selected_indices = []

        # 限制候选像素数量，避免选择过多像素
        max_pixels = min(max_candidate_pixels, len(sorted_indices))

        for j in range(max_pixels):
            idx = sorted_indices[j]
            cumulative_brightness += float(pixel_values[idx])
            selected_indices.append(idx)

            # 达到阈值或已选择最少1个像素时检查是否停止
            if cumulative_brightness >= brightness_threshold and len(selected_indices) >= 1:
                break

        # 如果累积亮度仍未达到阈值，至少选择1个最亮的像素
        if not selected_indices:
            selected_indices = [sorted_indices[0]]

        # logger.info(
        #     f"ion {i}: threshold={brightness_threshold}, cumulative_brightness={cumulative_brightness:.2f}, selected_indices={len(selected_indices)}")

        # 构造该离子的像素标记列表
        ion_pixel_marks = []
        for idx in selected_indices:
            x, y = int(x_coords[idx]), int(y_coords[idx])
            ion_pixel_marks.append([x, y])

        pixel_marks_for_ions.append(ion_pixel_marks)

    return pixel_marks_for_ions


def average_repeated_images(stacked_image: np.ndarray, repeat: int) -> np.ndarray:
    """
    输入纵向叠加的图片, 输出每个位置上repeat张图片的亮度均值构成的新图片。

    Parameters
    ----------
    stacked_image : np.ndarray
        形状为 [height * repeat, width] 的图片数组。
    repeat : int
        图片纵向重复的次数。

    Returns
    -------
    np.ndarray
        形状为 [height, width] 的单张图片, 每个位置为对应位置上repeat张图片的亮度均值。
    """
    if stacked_image.shape[0] % repeat != 0:
        raise ValueError("图片高度无法被repeat整除，请检查输入！")

    height = stacked_image.shape[0] // repeat
    width = stacked_image.shape[1]

    # 将图片 reshape 为 [repeat, height, width]
    reshaped_img = stacked_image.reshape(repeat, height, width)

    # 计算均值
    averaged_img = reshaped_img.mean(axis=0)

    return averaged_img


def sum_repeated_images(stacked_image: np.ndarray, repeat: int) -> np.ndarray:
    """
    输入纵向叠加的图片, 输出每个位置上repeat张图片的亮度总和构成的新图片。

    Parameters
    ----------
    stacked_image : np.ndarray
        形状为 [height * repeat, width] 的图片数组。
    repeat : int
        图片纵向重复的次数。

    Returns
    -------
    np.ndarray
        形状为 [height, width] 的单张图片, 每个位置为对应位置上repeat张图片的亮度总和。
    """
    if stacked_image.shape[0] % repeat != 0:
        raise ValueError("图片高度无法被repeat整除，请检查输入！")

    height = stacked_image.shape[0] // repeat
    width = stacked_image.shape[1]

    # 将图片 reshape 为 [repeat, height, width]
    reshaped_img = stacked_image.reshape(repeat, height, width)

    # 计算总和
    summed_img = reshaped_img.sum(axis=0)

    return summed_img

def normalize_to_255(matrix):
    """将矩阵线性归一化到0-255并转为uint8"""
    matrix_normalized = (
            255 * (matrix - np.min(matrix)) / (np.max(matrix) - np.min(matrix) + 1e-8)
    )  # 避免除零
    return matrix_normalized.astype(np.uint8)


def calculate_roi_pixel_sums(
        image: np.ndarray,
        roi_for_ions: List[List[List[int]]],
        background_noise: Union[float, List[float], np.ndarray] = 0.0,
) -> np.ndarray:
    """
    计算图像中多个离子标记像素点的像素值总和。

    Parameters
    ----------
    image : np.ndarray
        输入图像，形状为 [H, W]。
    roi_for_ions : List[List[List[int]]]
        离子像素标记列表，每个离子的格式为 [[x1, y1], [x2, y2], ...]。
    background_noise : float or List[float] or np.ndarray, optional
        底噪参数（默认=0.0）。

    Returns
    -------
    np.ndarray
        形状为 [1, N] 的数组，包含每个离子的像素值总和。
    """
    num_ions = len(roi_for_ions)
    bg_noise = np.asarray(background_noise)
    if bg_noise.ndim == 0:
        bg_noise = np.full(num_ions, bg_noise.item())
    else:
        bg_noise = bg_noise.flatten()[:num_ions]

    results = []
    for i, pixel_marks in enumerate(roi_for_ions):
        total_sum = 0.0
        for x, y in pixel_marks:
            if 0 <= y < image.shape[0] and 0 <= x < image.shape[1]:
                total_sum += float(image[y, x])
        
        # 扣除背景噪声
        total_sum -= len(pixel_marks) * bg_noise[i]
        results.append(total_sum)

    return np.array(results).reshape(1, -1)

def calculate_roi_pixel_sums_batch(
        image: np.ndarray,
        roi_for_ions: List[List[List[int]]],
        repeat: int,
        background_noise: Union[float, List[float], np.ndarray] = 0.0,
) -> np.ndarray:
    """
    计算堆叠图像中每个子图的多个离子标记像素点的像素和。

    Parameters
    ----------
    image : np.ndarray
        输入的堆叠图像，形状为 [repeat * height, width]
    roi_for_ions : List[List[List[int]]]
        离子像素标记列表，每个离子的格式为 [[x1, y1], [x2, y2], ...]。
    repeat : int
        垂直堆叠的子图数量
    background_noise : float or List[float] or np.ndarray, optional
        底噪参数（默认=0.0）

    Returns
    -------
    np.ndarray
        数组形状为 [repeat, num_ions]，包含每个子图中各离子的像素和
    """
    # 1. 获取总图像高度
    total_height = image.shape[0]
    if total_height % repeat != 0:
        raise ValueError("Image height must be divisible by repeat")

    # 2. 计算子图像高度
    sub_img_height = total_height // repeat
    sub_img_width = image.shape[1]
    # logger.info(f"sub_img_height_{sub_img_height}, sub_img_width {sub_img_width}")
    reshaped = image.reshape(repeat, sub_img_height, sub_img_width)  # 转换图像为 3d 的
    # logger.info(f"reshaped {reshaped}")
    num_ions = len(roi_for_ions)  # 获取离子个数
    bg_noise = np.asarray(background_noise, dtype=float)  # 获取灭个离子的背景噪声参数
    if bg_noise.ndim == 0:
        bg_noise = np.full(num_ions, float(bg_noise))
    else:
        bg_noise = bg_noise.flatten()[:num_ions]

    results = np.empty((repeat, num_ions), dtype=np.float64)  # 创建空 array 来存放数据

    for j, (pixel_marks, noise) in enumerate(zip(roi_for_ions, bg_noise)):
        for i in range(repeat):
            total_sum = 0.0
            for x, y in pixel_marks:
                if 0 <= y < sub_img_height and 0 <= x < sub_img_width:
                    # 只有当改坐标在子图内时, 才会计数
                    # logger.info(f"repeat_i {reshaped[i, y, x]}")
                    total_sum += float(reshaped[i, y, x])
                else:
                    # logger.warning(f"points {x,y} is not in picture")
                    pass
            # logger.info(f"total_sum_for_ions {total_sum}")
            results[i, j] = total_sum - noise # 减去每个离子区域的噪声

    return results

def calc_col_stats(data):
    """
    计算输入数据每一列的平均值、标准差、RMS、最大值和最小值

    参数：
    data: numpy 数组，形状为 [N, M]

    返回：
    numpy 数组，形状为 [5, M]
    行的顺序分别为：平均值、标准差、RMS、最大值和最小值
    """
    # 计算每一列的平均值（沿行方向计算）
    col_means = np.mean(data, axis=0)
    # 计算每一列的标准差
    col_stds = np.std(data, axis=0)
    # 计算每一列的 RMS：先求平方的平均值，再开根号
    col_rms = np.sqrt(np.mean(data ** 2, axis=0))
    # 计算每一列的最大值
    col_max = np.max(data, axis=0)
    # 计算每一列的最小值
    col_min = np.min(data, axis=0)

    # 合并结果为 [5, M] 的矩阵
    result = np.vstack((col_means, col_stds, col_rms, col_max, col_min))
    return result




def split_alternate_sources(
        stacked_image: np.ndarray, repeat: int
) -> tuple[np.ndarray, np.ndarray]:
    """
    将交替堆叠的两个图像来源分离为独立数组

    参数:
        stacked_image: 形状为 [总高度, 宽度] 的二维数组，数据按来源1、来源2交替堆叠
        repeat: 每个来源的重复次数（总图像对数为 repeat）

    返回:
        tuple: (source1_images, source2_images)，每个都是形状为 [repeat * 单张高度, 宽度] 的数组
    """
    total_height, width = stacked_image.shape

    # 检查总高度是否能被 2*repeat 整除
    if total_height % (2 * repeat) != 0:
        raise ValueError(
            f"input image  {total_height} must be decide by 2 * repeat ({2 * repeat})"
        )

    # 计算单张图像高度
    single_height = total_height // (2 * repeat)

    # 重塑为 [重复对数, 2来源, 单张高度, 宽度]
    reshaped = stacked_image.reshape(repeat, 2, single_height, width)

    # 分离来源1和来源2（按第二个维度拆分）
    source1_images = reshaped[:, 0, :, :].reshape(-1, width)  # 合并所有来源1的图像
    source2_images = reshaped[:, 1, :, :].reshape(-1, width)  # 合并所有来源2的图像

    return source1_images, source2_images

class IonStatus(IntEnum):
    """标记离子状态"""
    NORMAL = 0
    ION_LOST = 1
    ION_ATOMIZATION = 2
    ION_DARK = 3
    ION_INCREASE = 4

class QCMOS(HasEnvironment):
    """QCMOS 控制类"""

    def build(self):
        self.setattr_device("core")
        self.setattr_device("ccb")
        self.setattr_device("QCMOS_trigger")
        self.parameter = Config()

        self.init_roi = self.parameter.QCMOS.init_roi
        self.roi_for_ions = self.parameter.QCMOS.roi_for_ions
        self.ion_nums = len(self.roi_for_ions)

        self.shake_num = 0 # 为区分雾化和没离子, 设置 RF shake 次数, 大于 3 次还不行则认定是没离子

    @kernel()
    def initial(self, bundle_mode=1):
        self.QCMOS_trigger.output()
        self.QCMOS_trigger.off()
        self.core.wait_until_mu(now_mu())
        self.connect_qcmos(bundle_mode=bundle_mode)
        self.core.break_realtime()
        delay(3 * us)

    @rpc(flags={})
    def connect_qcmos(self, bundle_mode=1):
        if dcamcon_init():
            self.dcamcon = dcamcon_choose_and_open()
            if self.dcamcon is not None:
                # 设置相机参数
                # if bundle_num == 0:
                res = setup_properties(
                    self.dcamcon,
                    x_pos=self.init_roi[0],
                    y_pos=self.init_roi[1],
                    width=self.init_roi[2],
                    height=self.init_roi[3],
                    tirg_mode=0,
                    bundle_mode=bundle_mode,
                    bundle_number=self.parameter.QCMOS.bundle_num,
                )
                if res is False:
                    logger.info("Parameter Configure False")
                else:
                    logger.info("Parameter Configure Success")
            else:
                logger.info("Device Open Failure")
        else:
            dcamcon_uninit()
            logger.info("DCAM Initial Failure")

    @rpc(flags={})
    def read_data(self):
        """返回 repeat 张照片的数据, 结构为 [height * repeat, width]"""
        data = self.dcamcon.get_lastframedata()
        return data

    @rpc(flags={""})
    def read_data_double(self):
        """读数两次, 并返回其中的偶数项和奇数项矩阵
        用于一次循环中要读数两次的实验
        """
        data = self.dcamcon.get_lastframedata()
        # img = normalize_to_255(data)
        # img = Image.fromarray(img)
        # img.save('C:\\Users\\<USER>\\PycharmProjects\\ionctrl_develop\\debug_picture\\matrix_pil.png')
        # logger.info(f"data shape {data.shape}")
        odd, even = split_alternate_sources(data, repeat=self.parameter.QCMOS.bundle_num)
        #
        # odd_av = average_repeated_images(deepcopy(odd), repeat=100)
        # even_av = average_repeated_images(deepcopy(even), repeat=100)
        # odd_av = Image.fromarray(normalize_to_255(odd_av).astype('uint8'))
        # odd_av.save('C:\\Users\\<USER>\\PycharmProjects\\ionctrl_develop_new\\ionctrl_develop\\debug_picture\\matrix_odd_av.png')
        # even_av = Image.fromarray(normalize_to_255(even_av).astype('uint8'))
        # even_av.save('C:\\Users\\<USER>\\PycharmProjects\\ionctrl_develop_new\\ionctrl_develop\\debug_picture\\matrix_even_av.png')
        # logger.info(f"shape_odd {odd.shape}")
        # logger.info(f"shape_even {even.shape}")

        return odd, even

    @kernel()
    def qcmos_start_count(self):
        self.QCMOS_trigger.on()

    @kernel()
    def qcmos_stop_count(self):
        self.QCMOS_trigger.off()

    @kernel()
    def qcmos_trigger(self, trigger_num):
        """qcmos 触发"""
        for i in range(trigger_num):
            self.QCMOS_trigger.on()
            delay(self.parameter.Experiment.Cooling_Time)
            self.QCMOS_trigger.off()
            delay(self.parameter.Experiment.Cooling_Time)
        delay(3 * ms)

    @rpc(flags={})
    def prepare_for_idle(self):
        """img"""
        # 1. QCMOS 图像显示组件
        self.set_dataset(
            "img", np.zeros([self.init_roi[2], self.init_roi[3]]), broadcast=True
        )
        self.ccb.issue("create_applet", "IMG", "${artiq_applet}qcmos_image img")

        # 2. 离子计数直方图组件
        self.set_dataset("ion_count_index", np.arange(0, self.ion_nums), broadcast=True)
        self.set_dataset("ion_count", np.full(self.ion_nums, np.nan), broadcast=True)
        self.ccb.issue(
            "create_applet",
            "Ion_Counts",
            "${artiq_applet}green_plot_hist_8 ion_count --x ion_count_index",
        )

    @rpc(flags={})
    def process_for_idle_qcmos(self):
        """处理 Idle 类型的数据

        一个问题: QCMOS 的 idle 实验的目的是什么? 不把思维禁锢在 idle 实验上.
        在正式实验之前, 需要做一个离子索引到像素区域的映射. 该映射对应于 PMT 的多通道阈值处理.
        初始版本, 先只将图片做平均, 得到一个平均值.

        # 1. QCMOS cooling idle 的处理逻辑
        # 2.
        """
        # 1. 读数
        # start_time = time.perf_counter()
        init_image = self.read_data()  # shape = [height * repeat, width]
        # 2. 对每张图片计算亮度的平均值, 得到 shape = [height, width] 的平均亮度
        data = average_repeated_images(
            init_image, self.parameter.Experiment.Repeat
        )
        logger.info(f"max_data {np.max(data)}" )
        self.set_dataset("img", data.T, broadcast=True)

        # 注意：已移除detect_ion_regions函数，现在使用预配置的像素标记格式
        ion_counts = calculate_roi_pixel_sums(
            image=data, roi_for_ions=self.roi_for_ions, background_noise=0
        )

        start_time = time.perf_counter()
        ion_counts_matrix = calculate_roi_pixel_sums_batch(
            init_image,
            roi_for_ions=self.roi_for_ions,
            repeat=self.parameter.Experiment.Repeat,
            background_noise=self.parameter.QCMOS.bit16.background_noise,
        )
        cal_data = calc_col_stats(ion_counts_matrix)
        logger.info(f"mean, std, rms,max,min {cal_data}" )
        logger.info(f"time cost: {time.perf_counter() - start_time}")

        self.set_dataset("ion_count", ion_counts[0, :], broadcast=True)

    @rpc(flags={})
    def process_for_idle_qcmos_no_bundle(self):
        """处理 Idle 类型的数据

        一个问题: QCMOS 的 idle 实验的目的是什么? 不把思维禁锢在 idle 实验上.
        在正式实验之前, 需要做一个离子索引到像素区域的映射. 该映射对应于 PMT 的多通道阈值处理.
        初始版本, 先只将图片做平均, 得到一个平均值."""

        # 1. QCMOS cooling idle 的处理逻辑
        # 1. 读数, 绘图
        logger.info("data_get")
        logger.info(f"ion_num {self.ion_nums}" )
        data = self.read_data()  # shape = [height * repeat, width]
        self.set_dataset("img", data.T, broadcast=True)
        logger.info(self.roi_for_ions)
        ion_counts = calculate_roi_pixel_sums(data, self.roi_for_ions)
        logger.info(f"ion_counts {ion_counts}" )
        self.set_dataset("ion_count", ion_counts[0, :], broadcast=True)

    @rpc(flags={})
    def prepare_for_x_scan_counts(self, x_points):
        """one dimension scan"""
        # 1. Counts
        self.set_dataset("x_points", x_points, broadcast=True)
        self.set_dataset("y_counts", [], broadcast=True)
        self.ccb.issue(
            "create_applet",
            "X-Scan-Counts",
            "${artiq_applet}ionctrl_plot_multi_xy_8_color y_counts --x x_points",
        )  # 提交绘图组件

        # 2. QCMOS 图像显示组件
        self.set_dataset(
            "img", np.zeros([self.init_roi[0], self.init_roi[1]]), broadcast=True
        )
        self.ccb.issue("create_applet", "IMG", "${artiq_applet}qcmos_image img")

        # 3. 离子计数直方图组件
        self.set_dataset("ion_count_index", np.arange(0, self.ion_nums), broadcast=True)
        self.set_dataset("ion_count", np.full(self.ion_nums, np.nan), broadcast=True)
        self.ccb.issue(
            "create_applet",
            "Ion_Counts",
            "${artiq_applet}green_plot_hist_8 ion_count --x ion_count_index",
        )

    @rpc(flags={})
    def process_for_x_scan_counts(self, scan_point):
        """x_scan_counts 的数据处理

        1. 读取单张图像 (repeat * height, width), 亮度求和, 设置到 QCMOS;
        2. ROI 区域计数 (repeat, ion_nums); counts, 设置到离子计数绘图组件
        3. 数据处理:
            - 求和并放入实时 Ion_Counts 组件
            - 添加到 y_counts 里
        """
        # 1. 读数 repeat 组, 并设置到绘图组件
        init_image = self.read_data()  # shape = [height * repeat, width]
        sum_image = sum_repeated_images(
            init_image, self.parameter.Experiment.Repeat
        )
        self.set_dataset("img", sum_image.T, broadcast=True)

        # 2. ROI Counts
        ion_counts_matrix = calculate_roi_pixel_sums_batch(
            init_image,
            roi_for_ions=self.roi_for_ions,
            repeat=self.parameter.Experiment.Repeat,
            background_noise=self.parameter.QCMOS.bit16.background_noise,
        )
        cal_data = calc_col_stats(ion_counts_matrix)
        # 3.1 求和并放入实时 Ion_Counts 组件

        ion_counts = np.sum(ion_counts_matrix, axis=0).tolist()
        self.set_dataset("ion_count", ion_counts, broadcast=True)

        # 3.2 更新到 x_scan_counts 的
        data_len_ = len(self.get_dataset("y_counts"))

        if data_len_ <= scan_point:
            self.append_to_dataset("y_counts", ion_counts)
        else:
            self.mutate_dataset("y_counts", scan_point, ion_counts)

    @rpc(flags={})
    def prepare_for_x_scan(self, x_points):
        """不仅经是 x_scan, 还要做概率计算, 和 computation_basis 分析"""
        # 1. Counts
        self.set_dataset("x_points", x_points, broadcast=True)
        self.set_dataset("y_counts", [], broadcast=True)
        rid = int(list(self.scheduler.get_status().keys())[0])
        self.set_dataset('rid', rid, broadcast=True)

        self.ccb.issue(
            "create_applet",
            "X-Scan-Counts",
            "${artiq_applet}ionctrl_plot_multi_xy_8_color y_counts --x x_points --rid rid",
        )  # 提交绘图组件

        # 2. QCMOS 图像显示组件
        self.set_dataset(
            "img", np.zeros([self.init_roi[0], self.init_roi[1]]), broadcast=True
        )
        self.ccb.issue("create_applet", "IMG", "${artiq_applet}qcmos_image img")

        self.set_dataset(
            "img_cooling", np.zeros([self.init_roi[0], self.init_roi[1]]), broadcast=True
        )
        self.ccb.issue("create_applet", "IMG_cooling", "${artiq_applet}qcmos_image img_cooling")

        # 3. 离子计数直方图组件
        self.set_dataset("ion_count_index", np.arange(0, self.ion_nums), broadcast=True)
        self.set_dataset("ion_count", np.full(self.ion_nums, np.nan), broadcast=True)
        self.ccb.issue(
            "create_applet",
            "Ion_Counts",
            "${artiq_applet}green_plot_hist_8 ion_count --x ion_count_index",
        )

        # 4. 概率
        self.set_dataset("y_probability", [], broadcast=True)
        self.ccb.issue(
            "create_applet",
            "X-Scan-Probability",
            "${artiq_applet}ionctrl_plot_multi_xy_8_color y_probability --x x_points --rid rid",
        )  # 提交绘图组件


        # 5. 计算基
        self.set_dataset("computational_basis_probability", [], broadcast=True)
        self.ccb.issue(
            "create_applet",
            "X-Scan-Computational-Basis",
            "${artiq_applet}ionctrl_plot_multi_xy_8_color computational_basis_probability --x x_points --rid rid",
        )  # 提交绘图组件

        # 6. error_mitigation
        self.set_dataset("y_probability_mitigated", [], broadcast=True)
        self.ccb.issue(
            "create_applet",
            "X-Scan-Mitigated",
            "${artiq_applet}ionctrl_plot_multi_xy_8_color y_probability_mitigated --x x_points --rid rid",
        )  # 提交绘图组件

        self.set_dataset("computational_basis_histogram", [], broadcast=True)
        self.ccb.issue("create_applet", "Computational Basis Histogram",
                       "${artiq_applet}computational_basis_histogram computational_basis_histogram")

        # 7. 离子状态
        self.set_dataset("ion_lost", 0, broadcast=True)
        self.set_dataset("ion_status", 0, broadcast=True)

    @rpc(flags={})
    def process_for_single_bundle(self, re_repeat_num):
        """处理单个数据包并缓存, 用于支持大规模实验数据的分批处理

        实现实验数据的缓存机制, 将多次实验的数据累积存储, 供后续统一处理.
        主要解决QCMOS相机在bundle mode下数据量限制问题.

        Parameters
        ----------
        re_repeat_num : int
            重复实验的索引号. 当为0时表示新的实验开始, 会清空之前的缓存;
            非0时表示继续累积数据到现有缓存中.

        Notes
        -----
        1. 缓存机制:
           - 使用self.cache存储累积的实验数据
           - 首次调用或re_repeat_num=0时初始化缓存
           - 后续调用时通过vstack垂直堆叠新数据
        2. 数据结构:
           - 读取的数据格式为[height * repeat, width]的numpy数组
           - 累积后的缓存格式为[height * repeat * n, width], 其中n为累积的次数
        """
        if re_repeat_num == 0 or not hasattr(self, 'cache'):
            # 初始化缓存：首次运行或需要清空缓存时
            self.cache = self.read_data()
        else:
            # 累积数据：将新数据垂直堆叠到现有缓存
            self.cache = np.vstack((self.cache, self.read_data()))

    @rpc(flags={""})
    def process_for_single_bundle_double(self, re_repeat_num):
        """缓存图像数据，支持清空和动态堆叠

        Args:
            re_repeat_num:
                0 表示清空缓存并用新数据初始化缓存，
                其他值将新数据堆叠到缓存
        """
        # 读取数据（无论 re_repeat_num 为何值都需读取）
        # logger.info(f"bundle_num_readout {re_repeat_num}")
        odd, even = self.read_data_double()

        if re_repeat_num == 0 or not hasattr(self, 'odd_cache'):
            # 清空缓存或初始化时，直接用新数据覆盖
            self.odd_cache = odd
            self.even_cache = even
        else:
            # 非清空时，堆叠数据
            self.odd_cache = np.vstack((self.odd_cache, odd))
            self.even_cache = np.vstack((self.even_cache, even))

    @rpc(flags={})
    def process_for_x_scan(self, scan_point):
        """处理x轴扫描数据, 计算离子状态并更新相关数据集

        对缓存的图像数据进行处理, 计算ROI区域计数, 状态概率和计算基分析等.
        结果将更新到相应的数据集中, 用于实时显示和后续分析.

        Parameters
        ----------
        scan_point : int
            当前扫描点的索引, 用于确定数据在数据集中的位置.

        Notes
        -----
        处理流程:
        1. 从缓存读取图像数据(repeat * height, width), 亮度求和后更新到QCMOS显示
        2. 计算ROI区域计数(repeat, ion_nums), 更新到离子计数绘图组件
        3. 总计数处理:
           - 求和并放入实时Ion_Counts组件
           - 添加或更新到y_counts数据集
        4. 阈值处理, 将计数转换为布尔类型表示量子态
        5. 概率和计算基分析:
           - 计算并更新各离子处于亮态的概率
           - 进行计算基分析, 计算所有基态出现的概率分布
        """
        # 1. 读数 repeat 组, 并设置到绘图组件
        init_image = self.read_data()  # shape = [height * repeat, width]
        sum_image = sum_repeated_images(
            init_image, self.parameter.QCMOS.bundle_num
        )
        self.set_dataset("img", sum_image.T, broadcast=True)
        # 2. ROI Counts
        ion_counts_matrix = calculate_roi_pixel_sums_batch(
            init_image,
            roi_for_ions=self.roi_for_ions,
            repeat=self.parameter.QCMOS.bundle_num,
            background_noise=self.parameter.QCMOS.bit16.background_noise,
        )
        # 3.1 求和并放入实时 Ion_Counts 组件
        ion_counts = np.sum(ion_counts_matrix, axis=0).tolist()
        self.set_dataset("ion_count", ion_counts, broadcast=True)

        # 3.2 更新到 x_scan_counts 的
        data_len_ = len(self.get_dataset("y_counts"))

        if data_len_ <= scan_point:
            self.append_to_dataset("y_counts", ion_counts)
        else:
            self.mutate_dataset("y_counts", scan_point, ion_counts)
        # 4. 阈值矩阵计算,
        threshold_data = ion_counts_matrix > np.array(
            self.parameter.QCMOS.Detect_threshold
        )  # shape=[repeat, ion_nums], type=bool

        # 5. 更新到概率曲线
        probability = (
                np.sum(threshold_data, axis=0) / self.parameter.QCMOS.bundle_num
        ).tolist()
        data_len_ = len(self.get_dataset("y_probability"))

        if data_len_ <= scan_point:
            self.append_to_dataset("y_probability", probability)
        else:
            self.mutate_dataset("y_probability", scan_point, probability)

        # 6. 更新到 computational_basis_probability
        computational_basis_probability = (
                self.computational_basis_analysis_all(threshold_data)
                / self.parameter.QCMOS.bundle_num
        ).tolist()

        data_len_ = len(self.get_dataset("computational_basis_probability"))
        if data_len_ <= scan_point:
            self.append_to_dataset(
                "computational_basis_probability", computational_basis_probability
            )
        else:
            self.mutate_dataset(
                "computational_basis_probability",
                scan_point,
                computational_basis_probability,
            )

    @rpc(flags={})
    def process_for_x_scan_re_repeat(self, scan_point):
        """处理x轴扫描数据, 计算离子状态并更新相关数据集

        对缓存的图像数据进行处理, 计算ROI区域计数, 状态概率和计算基分析等.
        结果将更新到相应的数据集中, 用于实时显示和后续分析.

        Parameters
        ----------
        scan_point : int
            当前扫描点的索引, 用于确定数据在数据集中的位置.

        Notes
        -----
        处理流程:
        1. 从缓存读取图像数据(repeat * height, width), 亮度求和后更新到QCMOS显示
        2. 计算ROI区域计数(repeat, ion_nums), 更新到离子计数绘图组件
        3. 总计数处理:
           - 求和并放入实时Ion_Counts组件
           - 添加或更新到y_counts数据集
        4. 阈值处理, 将计数转换为布尔类型表示量子态
        5. 概率和计算基分析:
           - 计算并更新各离子处于亮态的概率
           - 进行计算基分析, 计算所有基态出现的概率分布
        """
        # 1. 读数 repeat 组, 并设置到绘图组件
        init_image = self.cache  # shape = [height * repeat, width]
        sum_image = sum_repeated_images(
            init_image, self.parameter.Experiment.Repeat
        )
        self.set_dataset("img", sum_image.T, broadcast=True)

        # 2. ROI Counts
        ion_counts_matrix = calculate_roi_pixel_sums_batch(
            init_image,
            roi_for_ions=self.roi_for_ions,
            repeat=self.parameter.Experiment.Repeat,
            background_noise=self.parameter.QCMOS.bit16.background_noise,
        )

        # 3.1 求和并放入实时 Ion_Counts 组件
        ion_counts = np.sum(ion_counts_matrix, axis=0).tolist()
        self.set_dataset("ion_count", ion_counts, broadcast=True)

        # 3.2 更新到 x_scan_counts 的
        data_len_ = len(self.get_dataset("y_counts"))

        if data_len_ <= scan_point:
            self.append_to_dataset("y_counts", ion_counts)
        else:
            self.mutate_dataset("y_counts", scan_point, ion_counts)
        # 4. 阈值矩阵计算,
        threshold_data = ion_counts_matrix > np.array(
            self.parameter.QCMOS.Detect_threshold
        )  # shape=[repeat, ion_nums], type=bool
        # 5. 更新到概率曲线
        probability = (
                np.sum(threshold_data, axis=0) / self.parameter.Experiment.Repeat
        ).tolist()
        data_len_ = len(self.get_dataset("y_probability"))

        if data_len_ <= scan_point:
            self.append_to_dataset("y_probability", probability)
        else:
            self.mutate_dataset("y_probability", scan_point, probability)

        # 6. 更新到 computational_basis_probability
        computational_basis_probability = (
                self.computational_basis_analysis_all(threshold_data)
                / self.parameter.Experiment.Repeat
        ).tolist()
        computational_basis_probability = np.array(computational_basis_probability)[:, 1].tolist()
        if len(computational_basis_probability) >=4:
            computational_basis_probability = np.array([computational_basis_probability[0],
                                                        computational_basis_probability[1] +
                                                        computational_basis_probability[2],
                                                        computational_basis_probability[3]]
                                                       )
        data_len_ = len(self.get_dataset("computational_basis_probability"))
        if data_len_ <= scan_point:
            self.append_to_dataset(
                "computational_basis_probability", computational_basis_probability
            )
        else:
            self.mutate_dataset(
                "computational_basis_probability",
                scan_point,
                computational_basis_probability,
            )

    @rpc(flags={})
    def process_for_x_scan_re_repeat_check_ion_lost(self, scan_point, ion_choice):
        """处理x轴扫描数据, 计算离子状态并更新相关数据集, 同时检查离子是否丢失

        对缓存的图像数据进行处理, 计算ROI区域计数, 状态概率和计算基分析等.
        结果将更新到相应的数据集中, 用于实时显示和后续分析.

        Parameters
        ----------
        scan_point : int
            当前扫描点的索引, 用于确定数据在数据集中的位置.

        Notes
        -----
        处理流程:
        1. 读出奇数缓存（探测态）与偶数缓存（cooling 态）, 各自的形状都是 (repeat * height, width);
        2. 对 cooling 态做处理, 做离子丢失检测;

        1. 从缓存读取图像数据(repeat * height, width), 亮度求和后更新到QCMOS显示
        2. 计算ROI区域计数(repeat, ion_nums), 更新到离子计数绘图组件
        3. 总计数处理:
           - 求和并放入实时Ion_Counts组件
           - 添加或更新到y_counts数据集
        4. 阈值处理, 将计数转换为布尔类型表示量子态
        5. 概率和计算基分析:
           - 计算并更新各离子处于亮态的概率
           - 进行计算基分析, 计算所有基态出现的概率分布
        """
        # 1. 读取 cooling 计数和 detecting 计数
        # logger.info("haha")
        cooling_data, detecting_data = self.odd_cache, self.even_cache
        # #
        # logger.info(f"cooling_shape: {cooling_data.shape}")
        # logger.info(f"detecting_shape: {detecting_data.shape}")
        # odd_av = average_repeated_images(deepcopy(cooling_data), repeat=self.parameter.Experiment.Repeat)
        # even_av = average_repeated_images(deepcopy(detecting_data), repeat=self.parameter.Experiment.Repeat)
        # odd_av = Image.fromarray(normalize_to_255(odd_av).astype('uint8'))
        # odd_av.save(f'C:\\Users\\<USER>\\PycharmProjects\\ionctrl_develop_new\\ionctrl_develop\\debug_picture\\matrix_odd_av_{scan_point}.png')
        # even_av = Image.fromarray(normalize_to_255(even_av).astype('uint8'))
        # even_av.save(f'C:\\Users\\<USER>\\PycharmProjects\\ionctrl_develop_new\\ionctrl_develop\\debug_picture\\matrix_even_av_{scan_point}.png')

        # if self.process_ion_lost(cooling_data) is True:
        #     return

        if self.cooling_data_process(cooling_data) is True:
            pass
        else:
            # 有异常直接跳过后续所有数据处理
            return

        init_image = detecting_data  # shape = [height * repeat, width]
        sum_image = sum_repeated_images(
            init_image, self.parameter.Experiment.Repeat
        )
        self.set_dataset("img", sum_image.T, broadcast=True)

        # 2. ROI Counts
        ion_counts_matrix = calculate_roi_pixel_sums_batch(
            init_image,
            roi_for_ions=self.roi_for_ions,
            repeat=self.parameter.Experiment.Repeat,
            background_noise=self.parameter.QCMOS.bit16.background_noise,
        )
        # logger.info(f"ion_counts_matrix{ion_counts_matrix}")
        # 2.1 ion_choice
        # if all(0 <= idx < ion_counts_matrix .shape[1] for idx in ion_choice):
        #     ion_counts_matrix = ion_counts_matrix[:, ion_choice]
        # else:
        #     logger.info("Qubit Index Not Exist. ")
        # 3.1 求和并放入实时 Ion_Counts 组件
        ion_counts = np.sum(ion_counts_matrix, axis=0).tolist()
        self.set_dataset("ion_count", ion_counts, broadcast=True)

        # 3.2 更新到 x_scan_counts 的
        data_len_ = len(self.get_dataset("y_counts"))

        if data_len_ <= scan_point:
            self.append_to_dataset("y_counts", ion_counts)
        else:
            self.mutate_dataset("y_counts", scan_point, ion_counts)
        # 4. 阈值矩阵计算,
        # threshold_data = ion_counts_matrix > np.array(
        #     self.parameter.QCMOS.Detect_threshold)[list(ion_choice)]  # shape=[repeat, ion_nums], type=bool
        threshold_data = ion_counts_matrix > np.array(
            self.parameter.QCMOS.Detect_threshold) # shape=[repeat, ion_nums], type=bool
        # 5. 更新到概率曲线
        probability = (np.sum(threshold_data, axis=0) / self.parameter.Experiment.Repeat)

        data_len_ = len(self.get_dataset("y_probability"))

        if data_len_ <= scan_point:
            self.append_to_dataset("y_probability", probability.tolist())
        else:
            self.mutate_dataset("y_probability", scan_point, probability.tolist())

        # 6. error mitigation
        probability_mitigation = 1 - np.array(probability)
        inverse_matrices = self.parameter.QCMOS.Errors.SPAM_matrix_inverted

        # 初始化修正后的概率（一维数组，形状 (7,)）
        corrected_bright_probs = np.zeros_like(probability)

        # 对每个离子进行修正（一维情况，无需 j 循环）
        for i in range(self.ion_nums):
            inv_mat = np.array(inverse_matrices[i])  # 当前离子的 SPAM 逆矩阵
            p_raw = np.array([probability_mitigation[i], probability[i]])  # [p_dark, p_bright]
            p_corrected = inv_mat @ p_raw  # 矩阵乘法修正
            corrected_bright_probs[i] = p_corrected[1]  # 取修正后的亮态概率

        data_len_ = len(self.get_dataset("y_probability_mitigated"))

        if data_len_ <= scan_point:
            self.append_to_dataset("y_probability_mitigated", corrected_bright_probs.tolist())
        else:
            self.mutate_dataset("y_probability_mitigated", scan_point, corrected_bright_probs.tolist())

        if all(0 <= idx < ion_counts_matrix .shape[1] for idx in ion_choice):
            ion_counts_matrix = ion_counts_matrix[:, ion_choice]
        else:
            logger.info("Qubit Index Not Exist. ")

        # 只有 computational_basis_probability 是需要 ion_choice
        threshold_data = ion_counts_matrix > np.array(
            self.parameter.QCMOS.Detect_threshold)[list(ion_choice)] # shape=[repeat, ion_nums], type=bool

        # 6. 更新到 computational_basis_probability
        computational_basis_probability = (
                self.computational_basis_analysis_all(threshold_data)
                / self.parameter.Experiment.Repeat
        ).tolist()


        ion_choice = np.asarray(ion_choice)
        if ion_choice.dtype == bool:
            ion_choice = np.where(ion_choice)[0]  # 布尔转整数
        elif isinstance(ion_choice, (tuple, list)):
            ion_choice = np.array(ion_choice, dtype=int)


        # 计算直积
        matrix = kronecker_product(inverse_matrices, ion_choice)

        computational_basis_probability = (matrix @ np.array(computational_basis_probability)[:, 1]).tolist()

        if len(computational_basis_probability) >=4:
            computational_basis_probability = np.array([computational_basis_probability[0],
                                                        computational_basis_probability[1],
                                                        computational_basis_probability[2],
                                                        computational_basis_probability[3]]
                                                       )

        data_len_ = len(self.get_dataset("computational_basis_probability"))
        if data_len_ <= scan_point:
            self.append_to_dataset("computational_basis_probability", computational_basis_probability)
        else:
            self.mutate_dataset("computational_basis_probability", scan_point, computational_basis_probability)


        computational_basis_histogram = self.computational_basis_analysis_all(threshold_data)


        # 执行矩阵乘法
        computational_basis_histogram = np.stack((computational_basis_histogram[:, 0], matrix @ computational_basis_histogram[:, 1]), axis=1)
        self.set_dataset("computational_basis_histogram", computational_basis_histogram, broadcast=True)

    @rpc(flags={})
    def process_ion_lost(self, cooling_data):
        """对 cooling data 做处理, 判断是否离子丢失
        1. 得到 ROI 计数矩阵 shape = [repeat, ion_num], 每个元素是一个离子的单次计数
        2. 阈值判断
        """

        ion_counts_matrix = calculate_roi_pixel_sums_batch(
            cooling_data,
            roi_for_ions=self.roi_for_ions,
            repeat=self.parameter.Experiment.Repeat,
            background_noise=self.parameter.QCMOS.bit16.background_noise)
        threshold_data = ion_counts_matrix > np.array(self.parameter.QCMOS.Detect_threshold)

        probability = (np.sum(threshold_data, axis=0) / self.parameter.Experiment.Repeat).tolist()

        # 3. 判断离子是否丢失
        for p in probability:
            if p < 0.3:
                self.set_dataset("ion_lost", 1, broadcast=True)

    @rpc(flags={})
    def process_for_ion_check(self):
        """在 ion_check 中调用
        1. 读取图像;
        2. 调用 cooling_data_process 处理数据;
        """
        # 1. 读取图像
        odd, even = self.read_data_double()

        # 2. 离子状态判断
        self.cooling_data_process(odd)

    @rpc(flags={})
    def cooling_data_process(self, cooling_data) -> TBool:
        """cooling 数据处理
        1. 亮离子数量识别 -> N, type = int;
        2. 判断与参考离子数 N_ref 的关系 -> 四种状态;
        3. 返回 True(离子数正常, 且无离子暗) / False (其他所有的异常);
        """
        # 1. 获得 cooling 数据
        cooling_frame = average_repeated_images(cooling_data, repeat=self.parameter.Experiment.Repeat)

        self.set_dataset("img_cooling", cooling_frame.T, broadcast=True)
        # 2. 获取 pumping 底噪数据
        pumping_frame = cv2.imread(self.parameter.QCMOS.background_image_path, cv2.IMREAD_GRAYSCALE) # 转化为单通道图

        # 3. 调用离子探测函数
        brightness_threshold = self.parameter.QCMOS.brightness_threshold
        max_candidate_pixels = self.parameter.QCMOS.max_candidate_pixels

        logger.info(f"cooling_shape: {cooling_frame.shape}")
        logger.info(f"pumping_shape: {pumping_frame.shape}")

        # 4. 得到 pixel_marks
        roi_for_ions = detect_brightest_pixels_from_avg(
            cooling_avg=cooling_frame,
            pumping_avg=pumping_frame,
            min_pixel_count=self.parameter.QCMOS.min_pixel_count,
            brightness_threshold=brightness_threshold,
            max_candidate_pixels=max_candidate_pixels
        )

        # 5. 得到离子数
        ion_num = len(roi_for_ions)
        if np.max(cooling_frame) < self.parameter.QCMOS.ion_exist_threshold:
            # 避免全暗图片因噪点识别错误
            ion_num = 0
            roi_for_ions = []
        ion_centers, _ = calculate_ion_centers(roi_for_ions)
        chain_center = self.parameter.QCMOS.ion_chain_center
        result = detect_dark_ions(ion_centers, chain_center, tolerance=0.3)


        logger.info(f"detect_ion_num: {ion_num}")
        logger.info(f"Optimal ion count: {result['best_n']}")
        logger.info(f"Fluorescent ions: {result['bright_count']}")
        logger.info(f"Dark ions: {result['dark_count']}")

        # 6. 判断:
        if ion_num == 0:
            if self.shake_num < 2:
                self.shake_num += 1
                logger.info("IonStatus.ION_ATOMIZATION")
                self.set_dataset("ion_status", IonStatus.ION_ATOMIZATION.value, broadcast=True)
            else:
                logger.info("IonStatus.ION_LOST")
                # self.shake_num = 0
                self.set_dataset("ion_status", IonStatus.ION_LOST.value, broadcast=True)

        elif 0 < ion_num < self.parameter.QCMOS.ion_num_ref:
            if result["dark_count"] > 0:
                logger.info("IonStatus.ION_ION_DARK")
                self.set_dataset("ion_status", IonStatus.ION_DARK.value, broadcast=True)

            elif result["dark_count"] == 0:
                logger.info("IonStatus.ION_LOST")
                self.set_dataset("ion_status", IonStatus.ION_LOST.value, broadcast=True)

            else:
                raise ValueError("Un_Support Value")

        elif ion_num == self.parameter.QCMOS.ion_num_ref:
            # 需插入正常与暗验证模块
            if result["dark_count"] > 0:
                logger.info("IonStatus.ION_ION_DARK")
                self.set_dataset("ion_status", IonStatus.ION_DARK.value, broadcast=True)

            else:
                logger.info("IonStatus.NORMAL")
                self.set_dataset("ion_status", IonStatus.NORMAL.value, broadcast=True)
                return True

        else:
            logger.info("IonStatus.ION_INCREASE")
            self.set_dataset("ion_status", IonStatus.ION_INCREASE.value, broadcast=True)

        return False

    @rpc(flags={})
    def check_lost(self) -> TBool:
        """检查离子是否丢失"""
        lost = self.get_dataset("ion_lost")
        logger.info(f"Ion_State: {lost}")
        return lost == 1

    @staticmethod
    def computational_basis_analysis_all(data):
        """分析量子比特计算基态的分布情况

        将布尔型量子态数据转换为计算基态的出现频率统计.
        每行代表一次实验, 将其转换为对应的基态十进制值, 
        然后统计所有可能基态的出现次数.

        Parameters
        ----------
        data : np.ndarray
            经过通道选择和阈值处理的布尔型数组, 形状为(M, N), 
            其中M是重复次数, N是离子(量子比特)个数.
            True表示亮态(|1⟩), False表示暗态(|0⟩).

        Returns
        -------
        np.ndarray
            形状为(2^N, 2)的数组, 第一列是基态的十进制索引(0到2^N-1),
            第二列是对应基态的出现次数.

        Notes
        -----
        实现原理:
        1. 基态编码: 通过矩阵乘法将布尔数组转换为十进制值
           - 每行的布尔值被视为二进制数的位
           - 使用位移和矩阵乘法高效计算十进制值
        2. 统计计数: 使用np.bincount统计每个十进制值出现的次数
        3. 结果格式化: 将索引和计数组合为二维数组返回
        """
        # 计算每一行对应的基的十进制表示
        state_ids = (data @ (1 << np.arange(data.shape[1] - 1, -1, -1))).astype(int)

        # 获取所有可能的基索引范围（从 0 到 2^N - 1）
        total_states = 1 << data.shape[1]

        # 使用 np.bincount 来高效统计基的出现次数
        counts = np.bincount(state_ids, minlength=total_states)

        # 构造与 computational_basis_analysis 一致的输出格式
        all_indices = np.arange(total_states)  # 生成所有基的索引 [0, 1, ..., 2^N-1]
        result = np.stack((all_indices, counts), axis=1)  # 组合为 L*2 的数组
        return result

    @staticmethod
    def computational_basis_analysis_all_1(data):
        """分析量子比特计算基态的分布情况(旧版本保留)

        将布尔型量子态数据转换为计算基态的出现频率统计.
        功能与computational_basis_analysis_all相同, 保留作为兼容目的.

        Parameters
        ----------
        data : np.ndarray
            经过通道选择和阈值处理的布尔型数组, 形状为(M, N), 
            其中M是重复次数, N是离子(量子比特)个数.
            True表示亮态(|1⟩), False表示暗态(|0⟩).

        Returns
        -------
        np.ndarray
            形状为(2^N, 2)的数组, 第一列是基态的十进制索引(0到2^N-1),
            第二列是对应基态的出现次数.

        Notes
        -----
        此方法是computational_basis_analysis_all的副本, 实现逻辑完全相同.
        保留此方法是为了保持向后兼容, 新代码应使用computational_basis_analysis_all.
        """
        # 计算每一行对应的基的十进制表示
        state_ids = (data @ (1 << np.arange(data.shape[1] - 1, -1, -1))).astype(int)

        # 获取所有可能的基索引范围（从 0 到 2^N - 1）
        total_states = 1 << data.shape[1]

        # 使用 np.bincount 来高效统计基的出现次数
        counts = np.bincount(state_ids, minlength=total_states)

        # 构造与 computational_basis_analysis 一致的输出格式
        all_indices = np.arange(total_states)  # 生成所有基的索引 [0, 1, ..., 2^N-1]
        result = np.stack((all_indices, counts), axis=1)  # 组合为 L*2 的数组
        return result

    @rpc(flags={"async"})
    def prepare_for_histogram(self):
        """ROI 数据集"""
        # 1. 差分图像显示
        self.set_dataset(
            "img", np.zeros([self.init_roi[0], self.init_roi[1]]), broadcast=True
        )
        self.ccb.issue("create_applet", "IMG", "${artiq_applet}qcmos_image img")

        # 2. ion_counts 显示
        self.set_dataset("ion_count_index", [0, 1, 2], broadcast=True)
        self.set_dataset("ion_count", [0, 0, 0], broadcast=True)
        self.ccb.issue(
            "create_applet",
            "Ion_Counts",
            "${artiq_applet}green_plot_hist_8 ion_count --x ion_count_index",
        )
        self.set_dataset("ion_lost", 0, broadcast=True)

        # 3. 用于存放校准 roi 参数
        self.set_dataset("new_roi", [], broadcast=True)

    @rpc(flags={""})
    def process_for_roi_calibration(self):
        """离子 ROI 校准
        """
        # 1. 读数 cooling + pumping
        odd, even = self.read_data_double()
        if self.process_ion_lost(odd) is True:
            return
        # 2. 均值计算
        cooling_frame = average_repeated_images(odd, repeat=self.parameter.Experiment.Repeat)
        pumping_frame = average_repeated_images(even, repeat=self.parameter.Experiment.Repeat)
        # 构造差分图像
        diff_frame = cooling_frame - pumping_frame
        self.set_dataset(
            "img", diff_frame.T, broadcast=True
        )

        # 3. 使用亮度阈值像素标记格式
        brightness_threshold = self.parameter.QCMOS.brightness_threshold
        max_candidate_pixels = self.parameter.QCMOS.max_candidate_pixels

        logger.info(f"ROI calibration parameter:")
        logger.info(f"  -  brightness_threshold: {brightness_threshold}")
        logger.info(f"  - max_candidate_pixels: {max_candidate_pixels}")

        pixel_marks = detect_brightest_pixels_from_avg(
            cooling_avg=cooling_frame,
            pumping_avg=pumping_frame,
            min_pixel_count=self.parameter.QCMOS.min_pixel_count,
            brightness_threshold=brightness_threshold,
            max_candidate_pixels=max_candidate_pixels
        )

        self.parameter.QCMOS.roi_for_ions = pixel_marks

        self.set_dataset("new_roi", pad_pixel_marks(pixel_marks), persist=True, broadcast=True)
        self.parameter.QCMOS.bit16.background_noise = [190 * len(roi) for roi in pixel_marks]  # 每个像素默认 190 的底噪
        self.set_dataset("background_noise", [190 * len(roi) for roi in pixel_marks])
        logger.info("Pixel marks calibration data", pixel_marks)
        logger.info(f"Number of ions detected: {len(pixel_marks)}")

        # 打印每个离子的像素数量
        for i, ion_pixels in enumerate(pixel_marks):
            logger.info(f"  - ion {i + 1}: {len(ion_pixels)} pixel")

        # 更新 ion_counts
        ion_counts = calculate_roi_pixel_sums(
            image=diff_frame, roi_for_ions=self.roi_for_ions, background_noise=0
        )
        logger.info("ion_count", ion_counts)
        self.set_dataset("ion_count", ion_counts[0, :], broadcast=True)
        self.set_dataset("ion_count_index", np.arange(0, self.ion_nums), broadcast=True)

    @rpc(flags={""})
    def process_for_histogram(self, ion=(0,), re_repeat=1):
        """Histogram 数据处理
        流程:
        1. 读数两次, 并分割成 pump_detect 和 control_detect 图像, shape 均为 (repeat * height, width);
        2. ROI 求和, 分别得到 pump_detect 和 control_detect 对应于离子这一层的计数, shape 均为 (repeat, ion_num);
        3. 选择要做 histogram 的离子索引, 并对他们的通道进一步求和, 对 pump_detect 和 control_detect 分别得到 shape = (repeat, 1),
        本步骤假定多个离子共用同一个阈值;
        4. 将 pump_detect 和 control_detect 的两列数拼成一个矩阵分别做 histogram, 得到 shape = (bins, 2) 的数据;
        5. 根据设定的阈值分别计算二者的错误率, 得到 shape = (2,1) 的 list;
        6. 更新数据集 error 和 histogram;

        结果信息:
        得到一个矩阵: shape = [N, 3]
        """
        # 1. 读数两次, 分别获取 pumping data 和 control data
        pump_detect_data_temp, control_detect_data_temp = self.odd_cache, self.even_cache
        # 2. 将 pump -> detect 和 control -> detect 的数据分开, 分别做 ROI 求和
        pump_matrix = calculate_roi_pixel_sums_batch(pump_detect_data_temp, self.roi_for_ions,
                                                     repeat=self.parameter.Experiment.Repeat * re_repeat,
                                                     background_noise=self.parameter.QCMOS.bit16.background_noise)
        ctrl_matrix = calculate_roi_pixel_sums_batch(control_detect_data_temp, self.roi_for_ions,
                                                     repeat=self.parameter.Experiment.Repeat * re_repeat,
                                                     background_noise=self.parameter.QCMOS.bit16.background_noise)

        # ----- 选择要处理的离子(选择所有离子) -----
        ion_choice = range(pump_matrix.shape[1])
        ion_choice = list(ion_choice)  # 转换为列表方便处理

        # 初始化用于存储结果的列表
        histograms: List[np.ndarray] = []  # 存储每个离子的直方图 (暗态, 亮态)
        thresholds: List[int] = []  # 存储每个离子的最优阈值
        errors_dark_to_bright: List[float] = []  # 存储每个离子的暗态判亮错误率
        errors_bright_to_dark: List[float] = []  # 存储每个离子的亮态判暗错误率
        # ---------- 遍历每个选定的离子 ----------
        for i in ion_choice:
            # 提取当前离子的暗态和亮态计数数据
            pump_counts = pump_matrix[:, i]
            ctrl_counts = ctrl_matrix[:, i]

            # 合并暗态和亮态数据以确定直方图的范围和 bin 数量
            all_counts = np.concatenate([pump_counts, ctrl_counts])
            # 确定直方图的 bin 数量：始终根据数据范围自动计算
            # 确保至少有一个 bin
            data_range = all_counts.max() - 0
            nbins = int(data_range + 1) if data_range >= 0 else 1

            # 生成暗态直方图
            hist_dark = np.histogram(
                pump_counts, bins=nbins, range=(0, all_counts.max())
            )[0]
            # 生成亮态直方图
            hist_bright = np.histogram(
                ctrl_counts, bins=nbins, range=(0, all_counts.max())
            )[0]

            # 将暗态和亮态直方图堆叠在一起
            hist_i = np.stack((hist_dark, hist_bright), axis=1)
            histograms.append(hist_i)

            # 计算当前离子的最优阈值和错误率
            t_star, p_d2b, p_b2d = _optimal_threshold_1d(hist_dark, hist_bright)
            thresholds.append(t_star)
            errors_dark_to_bright.append(p_d2b)
            errors_bright_to_dark.append(p_b2d)
        self.parameter.QCMOS.Detect_threshold = np.array(thresholds)
        self.parameter.QCMOS.Errors.Dark_to_Bright = np.array(errors_dark_to_bright)
        self.parameter.QCMOS.Errors.Bright_to_Dark = np.array(errors_bright_to_dark)
        self.parameter.QCMOS.Errors.SPAM = (np.array(errors_dark_to_bright) + np.array(errors_bright_to_dark)) / 2
        self.set_dataset("SPAM", self.parameter.QCMOS.Errors.SPAM, broadcast=True)
        self.set_dataset("Detect_threshold", self.parameter.QCMOS.Detect_threshold, broadcast=True)
        self.set_dataset("Errors_Dark_to_Bright", self.parameter.QCMOS.Errors.Dark_to_Bright, broadcast=True)
        self.set_dataset("Errors_Bright_to_Dark", self.parameter.QCMOS.Errors.Bright_to_Dark, broadcast=True)

        if len(ion) != 1:
            logger.info("Ion_choice should be 1")
        self.set_dataset("histogram", histograms[ion[0]].tolist(), broadcast=True)
        self.set_dataset(
            "error", [errors_dark_to_bright[ion[0]], errors_bright_to_dark[ion[0]]], broadcast=True
        )
        self.set_dataset("histogram_threshold", thresholds[ion[0]], broadcast=True)

    @staticmethod
    def _compute_histogram(data, bins=26):
        """计算二列数据的直方图分布

        将N×2矩阵的两列数据分别计算直方图, 用于量子态判决分析.
        适用于分析离子荧光强度分布, 特别是暗态和亮态的分布情况.

        Parameters
        ----------
        data : np.ndarray
            输入数据矩阵, 形状为(N, 2), 通常第一列为暗态数据, 第二列为亮态数据.
        bins : int, optional
            直方图的分箱数量, 默认为26. 同时也用作数据范围的上限(0到bins-1).

        Returns
        -------
        np.ndarray
            形状为(bins, 2)的数组, 包含两列数据的直方图统计结果.
            第一列对应输入data的第一列(暗态), 第二列对应输入data的第二列(亮态).

        Notes
        -----
        1. 数据范围固定为0到bins-1, 适合归一化后的计数数据
        2. 直方图结果可用于:
           - 判断最佳阈值位置
           - 计算判决错误率
           - 可视化暗亮态分布
        """
        hist_data_0, bin_edges_0 = np.histogram(
            data[:, 0], bins=bins, range=(0, bins - 1)
        )
        hist_data_10, bin_edges_10 = np.histogram(
            data[:, 1], bins=bins, range=(0, bins - 1)
        )
        return np.column_stack((hist_data_0, hist_data_10))
