version = 1
revision = 2
requires-python = ">=3.11"

[[package]]
name = "artiq"
version = "8.2.8"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
dependencies = [
    { name = "artiq-comtools" },
    { name = "h5py" },
    { name = "le<PERSON><PERSON><PERSON>" },
    { name = "llvmlite" },
    { name = "lmdb" },
    { name = "matplotlib" },
    { name = "numpy" },
    { name = "prettytable" },
    { name = "pygit2" },
    { name = "pyqtgraph" },
    { name = "python-dateutil" },
    { name = "pythonparser" },
    { name = "qasync" },
    { name = "scipy" },
    { name = "sipyco" },
]
sdist = { url = "http://***************:3141/zyx/devpi/+f/ab3/d44133d51fe64/artiq-8.2.8.tar.gz", hash = "sha256:ab3d44133d51fe647d5b9d68e818bbdb636b9a33a70ae964fc1b7e9dfefe3ee3" }
wheels = [
    { url = "http://***************:3141/zyx/devpi/+f/bbc/7a0f6eba66281/artiq-8.2.8-py3-none-any.whl", hash = "sha256:bbc7a0f6eba6628152f177cfb79e38cd679369e17d88b1dc8ce51c97ec5b0689" },
]

[[package]]
name = "artiq-comtools"
version = "1.3.1"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
sdist = { url = "http://***************:3141/zyx/devpi/+f/362/8ceb46067f0f1/artiq_comtools-1.3.1.tar.gz", hash = "sha256:3628ceb46067f0f1f67ff30a4ca2c4f3b1db26121310cf3f690d93d87289df86" }
wheels = [
    { url = "http://***************:3141/zyx/devpi/+f/4c3/8c5f4c0e62ea3/artiq_comtools-1.3.1-py3-none-any.whl", hash = "sha256:4c38c5f4c0e62ea3cc2f49b7b16530a2efb18476ed3dbb38611b6969b261c25a" },
]

[[package]]
name = "cffi"
version = "1.17.1"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
dependencies = [
    { name = "pycparser" },
]
sdist = { url = "http://***************:3141/zyx/pypi/+f/1c3/9c6016c32bc48/cffi-1.17.1.tar.gz", hash = "sha256:1c39c6016c32bc48dd54561950ebd6836e1670f2ae46128f67cf49e789c52824" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/a45/e3c6913c5b87b/cffi-1.17.1-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:a45e3c6913c5b87b3ff120dcdc03f6131fa0065027d0ed7ee6190736a74cd401" },
    { url = "http://***************:3141/zyx/pypi/+f/30c/5e0cb5ae493c0/cffi-1.17.1-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:30c5e0cb5ae493c04c8b42916e52ca38079f1b235c2f8ae5f4527b963c401caf" },
    { url = "http://***************:3141/zyx/pypi/+f/f75/c7ab1f9e4aca5/cffi-1.17.1-cp311-cp311-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:f75c7ab1f9e4aca5414ed4d8e5c0e303a34f4421f8a0d47a4d019ceff0ab6af4" },
    { url = "http://***************:3141/zyx/pypi/+f/a1e/d2dd297264149/cffi-1.17.1-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:a1ed2dd2972641495a3ec98445e09766f077aee98a1c896dcb4ad0d303628e41" },
    { url = "http://***************:3141/zyx/pypi/+f/46b/f43160c1a35f7/cffi-1.17.1-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:46bf43160c1a35f7ec506d254e5c890f3c03648a4dbac12d624e4490a7046cd1" },
    { url = "http://***************:3141/zyx/pypi/+f/a24/ed04c8ffd54b0/cffi-1.17.1-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:a24ed04c8ffd54b0729c07cee15a81d964e6fee0e3d4d342a27b020d22959dc6" },
    { url = "http://***************:3141/zyx/pypi/+f/610/faea79c43e44c/cffi-1.17.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:610faea79c43e44c71e1ec53a554553fa22321b65fae24889706c0a84d4ad86d" },
    { url = "http://***************:3141/zyx/pypi/+f/a9b/15d491f3ad5d6/cffi-1.17.1-cp311-cp311-musllinux_1_1_aarch64.whl", hash = "sha256:a9b15d491f3ad5d692e11f6b71f7857e7835eb677955c00cc0aefcd0669adaf6" },
    { url = "http://***************:3141/zyx/pypi/+f/de2/ea4b583362538/cffi-1.17.1-cp311-cp311-musllinux_1_1_i686.whl", hash = "sha256:de2ea4b5833625383e464549fec1bc395c1bdeeb5f25c4a3a82b5a8c756ec22f" },
    { url = "http://***************:3141/zyx/pypi/+f/fc4/8c783f9c87e60/cffi-1.17.1-cp311-cp311-musllinux_1_1_x86_64.whl", hash = "sha256:fc48c783f9c87e60831201f2cce7f3b2e4846bf4d8728eabe54d60700b318a0b" },
    { url = "http://***************:3141/zyx/pypi/+f/85a/950a4ac9c3593/cffi-1.17.1-cp311-cp311-win32.whl", hash = "sha256:85a950a4ac9c359340d5963966e3e0a94a676bd6245a4b55bc43949eee26a655" },
    { url = "http://***************:3141/zyx/pypi/+f/caa/f0640ef5f5517/cffi-1.17.1-cp311-cp311-win_amd64.whl", hash = "sha256:caaf0640ef5f5517f49bc275eca1406b0ffa6aa184892812030f04c2abf589a0" },
    { url = "http://***************:3141/zyx/pypi/+f/805/b4371bf7197c3/cffi-1.17.1-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:805b4371bf7197c329fcb3ead37e710d1bca9da5d583f5073b799d5c5bd1eee4" },
    { url = "http://***************:3141/zyx/pypi/+f/733/e99bc2df47476/cffi-1.17.1-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:733e99bc2df47476e3848417c5a4540522f234dfd4ef3ab7fafdf555b082ec0c" },
    { url = "http://***************:3141/zyx/pypi/+f/125/7bdabf294dceb/cffi-1.17.1-cp312-cp312-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:1257bdabf294dceb59f5e70c64a3e2f462c30c7ad68092d01bbbfb1c16b1ba36" },
    { url = "http://***************:3141/zyx/pypi/+f/da9/5af8214998d77/cffi-1.17.1-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:da95af8214998d77a98cc14e3a3bd00aa191526343078b530ceb0bd710fb48a5" },
    { url = "http://***************:3141/zyx/pypi/+f/d63/afe322132c194/cffi-1.17.1-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:d63afe322132c194cf832bfec0dc69a99fb9bb6bbd550f161a49e9e855cc78ff" },
    { url = "http://***************:3141/zyx/pypi/+f/f79/fc4fc25f1c869/cffi-1.17.1-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:f79fc4fc25f1c8698ff97788206bb3c2598949bfe0fef03d299eb1b5356ada99" },
    { url = "http://***************:3141/zyx/pypi/+f/b62/ce867176a75d0/cffi-1.17.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:b62ce867176a75d03a665bad002af8e6d54644fad99a3c70905c543130e39d93" },
    { url = "http://***************:3141/zyx/pypi/+f/386/c8bf53c502fff/cffi-1.17.1-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:386c8bf53c502fff58903061338ce4f4950cbdcb23e2902d86c0f722b786bbe3" },
    { url = "http://***************:3141/zyx/pypi/+f/4ce/b10419a9adf44/cffi-1.17.1-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:4ceb10419a9adf4460ea14cfd6bc43d08701f0835e979bf821052f1805850fe8" },
    { url = "http://***************:3141/zyx/pypi/+f/a08/d7e755f8ed210/cffi-1.17.1-cp312-cp312-win32.whl", hash = "sha256:a08d7e755f8ed21095a310a693525137cfe756ce62d066e53f502a83dc550f65" },
    { url = "http://***************:3141/zyx/pypi/+f/513/92eae71afec0d/cffi-1.17.1-cp312-cp312-win_amd64.whl", hash = "sha256:51392eae71afec0d0c8fb1a53b204dbb3bcabcb3c9b807eedf3e1e6ccf2de903" },
    { url = "http://***************:3141/zyx/pypi/+f/f3a/2b4222ce6b60e/cffi-1.17.1-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:f3a2b4222ce6b60e2e8b337bb9596923045681d71e5a082783484d845390938e" },
    { url = "http://***************:3141/zyx/pypi/+f/098/4a4925a435b1d/cffi-1.17.1-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:0984a4925a435b1da406122d4d7968dd861c1385afe3b45ba82b750f229811e2" },
    { url = "http://***************:3141/zyx/pypi/+f/d01/b12eeeb4427d3/cffi-1.17.1-cp313-cp313-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:d01b12eeeb4427d3110de311e1774046ad344f5b1a7403101878976ecd7a10f3" },
    { url = "http://***************:3141/zyx/pypi/+f/706/510fe141c86a6/cffi-1.17.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:706510fe141c86a69c8ddc029c7910003a17353970cff3b904ff0686a5927683" },
    { url = "http://***************:3141/zyx/pypi/+f/de5/5b766c7aa2e2a/cffi-1.17.1-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:de55b766c7aa2e2a3092c51e0483d700341182f08e67c63630d5b6f200bb28e5" },
    { url = "http://***************:3141/zyx/pypi/+f/c59/d6e989d074601/cffi-1.17.1-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:c59d6e989d07460165cc5ad3c61f9fd8f1b4796eacbd81cee78957842b834af4" },
    { url = "http://***************:3141/zyx/pypi/+f/dd3/98dbc6773384a/cffi-1.17.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:dd398dbc6773384a17fe0d3e7eeb8d1a21c2200473ee6806bb5e6a8e62bb73dd" },
    { url = "http://***************:3141/zyx/pypi/+f/3ed/c8d958eb099c6/cffi-1.17.1-cp313-cp313-musllinux_1_1_aarch64.whl", hash = "sha256:3edc8d958eb099c634dace3c7e16560ae474aa3803a5df240542b305d14e14ed" },
    { url = "http://***************:3141/zyx/pypi/+f/72e/72408cad3d541/cffi-1.17.1-cp313-cp313-musllinux_1_1_x86_64.whl", hash = "sha256:72e72408cad3d5419375fc87d289076ee319835bdfa2caad331e377589aebba9" },
    { url = "http://***************:3141/zyx/pypi/+f/e03/eab0a8677fa80/cffi-1.17.1-cp313-cp313-win32.whl", hash = "sha256:e03eab0a8677fa80d646b5ddece1cbeaf556c313dcfac435ba11f107ba117b5d" },
    { url = "http://***************:3141/zyx/pypi/+f/f6a/16c31041f09ea/cffi-1.17.1-cp313-cp313-win_amd64.whl", hash = "sha256:f6a16c31041f09ead72d69f583767292f750d24913dadacf5756b966aacb3f1a" },
]

[[package]]
name = "contourpy"
version = "1.3.3"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
dependencies = [
    { name = "numpy" },
]
sdist = { url = "http://***************:3141/zyx/pypi/+f/083/e12155b210502/contourpy-1.3.3.tar.gz", hash = "sha256:083e12155b210502d0bca491432bb04d56dc3432f95a979b429f2848c3dbe880" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/709/a48ef9a690e13/contourpy-1.3.3-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:709a48ef9a690e1343202916450bc48b9e51c049b089c7f79a267b46cffcdaa1" },
    { url = "http://***************:3141/zyx/pypi/+f/234/16f38bfd74d5d/contourpy-1.3.3-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:23416f38bfd74d5d28ab8429cc4d63fa67d5068bd711a85edb1c3fb0c3e2f381" },
    { url = "http://***************:3141/zyx/pypi/+f/929/ddf8c4c7f348e/contourpy-1.3.3-cp311-cp311-manylinux_2_26_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:929ddf8c4c7f348e4c0a5a3a714b5c8542ffaa8c22954862a46ca1813b667ee7" },
    { url = "http://***************:3141/zyx/pypi/+f/9e9/99574eddae35f/contourpy-1.3.3-cp311-cp311-manylinux_2_26_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:9e999574eddae35f1312c2b4b717b7885d4edd6cb46700e04f7f02db454e67c1" },
    { url = "http://***************:3141/zyx/pypi/+f/0bf/67e0e3f482cb6/contourpy-1.3.3-cp311-cp311-manylinux_2_26_s390x.manylinux_2_28_s390x.whl", hash = "sha256:0bf67e0e3f482cb69779dd3061b534eb35ac9b17f163d851e2a547d56dba0a3a" },
    { url = "http://***************:3141/zyx/pypi/+f/51e/79c1f7470158e/contourpy-1.3.3-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:51e79c1f7470158e838808d4a996fa9bac72c498e93d8ebe5119bc1e6becb0db" },
    { url = "http://***************:3141/zyx/pypi/+f/598/c3aaece21c503/contourpy-1.3.3-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:598c3aaece21c503615fd59c92a3598b428b2f01bfb4b8ca9c4edeecc2438620" },
    { url = "http://***************:3141/zyx/pypi/+f/322/ab1c99b008dad/contourpy-1.3.3-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:322ab1c99b008dad206d406bb61d014cf0174df491ae9d9d0fac6a6fda4f977f" },
    { url = "http://***************:3141/zyx/pypi/+f/fd9/07ae12cd483cd/contourpy-1.3.3-cp311-cp311-win32.whl", hash = "sha256:fd907ae12cd483cd83e414b12941c632a969171bf90fc937d0c9f268a31cafff" },
    { url = "http://***************:3141/zyx/pypi/+f/351/9428f6be58431/contourpy-1.3.3-cp311-cp311-win_amd64.whl", hash = "sha256:3519428f6be58431c56581f1694ba8e50626f2dd550af225f82fb5f5814d2a42" },
    { url = "http://***************:3141/zyx/pypi/+f/15f/f10bfada4bf92/contourpy-1.3.3-cp311-cp311-win_arm64.whl", hash = "sha256:15ff10bfada4bf92ec8b31c62bf7c1834c244019b4a33095a68000d7075df470" },
    { url = "http://***************:3141/zyx/pypi/+f/b08/a32ea2f8e42cf/contourpy-1.3.3-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:b08a32ea2f8e42cf1d4be3169a98dd4be32bafe4f22b6c4cb4ba810fa9e5d2cb" },
    { url = "http://***************:3141/zyx/pypi/+f/556/dba8fb6f5d874/contourpy-1.3.3-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:556dba8fb6f5d8742f2923fe9457dbdd51e1049c4a43fd3986a0b14a1d815fc6" },
    { url = "http://***************:3141/zyx/pypi/+f/92d/9abc807cf7d0e/contourpy-1.3.3-cp312-cp312-manylinux_2_26_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:92d9abc807cf7d0e047b95ca5d957cf4792fcd04e920ca70d48add15c1a90ea7" },
    { url = "http://***************:3141/zyx/pypi/+f/b2e/8faa0ed68cb29/contourpy-1.3.3-cp312-cp312-manylinux_2_26_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:b2e8faa0ed68cb29af51edd8e24798bb661eac3bd9f65420c1887b6ca89987c8" },
    { url = "http://***************:3141/zyx/pypi/+f/626/d60935cf668e7/contourpy-1.3.3-cp312-cp312-manylinux_2_26_s390x.manylinux_2_28_s390x.whl", hash = "sha256:626d60935cf668e70a5ce6ff184fd713e9683fb458898e4249b63be9e28286ea" },
    { url = "http://***************:3141/zyx/pypi/+f/4d0/0e655fcef08ab/contourpy-1.3.3-cp312-cp312-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:4d00e655fcef08aba35ec9610536bfe90267d7ab5ba944f7032549c55a146da1" },
    { url = "http://***************:3141/zyx/pypi/+f/451/e71b5a7d59737/contourpy-1.3.3-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:451e71b5a7d597379ef572de31eeb909a87246974d960049a9848c3bc6c41bf7" },
    { url = "http://***************:3141/zyx/pypi/+f/459/c1f020cd59fcf/contourpy-1.3.3-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:459c1f020cd59fcfe6650180678a9993932d80d44ccde1fa1868977438f0b411" },
    { url = "http://***************:3141/zyx/pypi/+f/023/b44101dfe49d7/contourpy-1.3.3-cp312-cp312-win32.whl", hash = "sha256:023b44101dfe49d7d53932be418477dba359649246075c996866106da069af69" },
    { url = "http://***************:3141/zyx/pypi/+f/815/3b8bfc11e1e4d/contourpy-1.3.3-cp312-cp312-win_amd64.whl", hash = "sha256:8153b8bfc11e1e4d75bcb0bff1db232f9e10b274e0929de9d608027e0d34ff8b" },
    { url = "http://***************:3141/zyx/pypi/+f/07c/e5ed73ecdc4a0/contourpy-1.3.3-cp312-cp312-win_arm64.whl", hash = "sha256:07ce5ed73ecdc4a03ffe3e1b3e3c1166db35ae7584be76f65dbbe28a7791b0cc" },
    { url = "http://***************:3141/zyx/pypi/+f/177/fb367556747a6/contourpy-1.3.3-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:177fb367556747a686509d6fef71d221a4b198a3905fe824430e5ea0fda54eb5" },
    { url = "http://***************:3141/zyx/pypi/+f/d00/2b6f00d73d693/contourpy-1.3.3-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:d002b6f00d73d69333dac9d0b8d5e84d9724ff9ef044fd63c5986e62b7c9e1b1" },
    { url = "http://***************:3141/zyx/pypi/+f/348/ac1f5d4f1d66d/contourpy-1.3.3-cp313-cp313-manylinux_2_26_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:348ac1f5d4f1d66d3322420f01d42e43122f43616e0f194fc1c9f5d830c5b286" },
    { url = "http://***************:3141/zyx/pypi/+f/655/456777ff65c2c/contourpy-1.3.3-cp313-cp313-manylinux_2_26_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:655456777ff65c2c548b7c454af9c6f33f16c8884f11083244b5819cc214f1b5" },
    { url = "http://***************:3141/zyx/pypi/+f/644/a6853d15b2512/contourpy-1.3.3-cp313-cp313-manylinux_2_26_s390x.manylinux_2_28_s390x.whl", hash = "sha256:644a6853d15b2512d67881586bd03f462c7ab755db95f16f14d7e238f2852c67" },
    { url = "http://***************:3141/zyx/pypi/+f/4de/bd64f124ca620/contourpy-1.3.3-cp313-cp313-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:4debd64f124ca62069f313a9cb86656ff087786016d76927ae2cf37846b006c9" },
    { url = "http://***************:3141/zyx/pypi/+f/a15/459b0f4615b00/contourpy-1.3.3-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:a15459b0f4615b00bbd1e91f1b9e19b7e63aea7483d03d804186f278c0af2659" },
    { url = "http://***************:3141/zyx/pypi/+f/ca0/fdcd73925568c/contourpy-1.3.3-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:ca0fdcd73925568ca027e0b17ab07aad764be4706d0a925b89227e447d9737b7" },
    { url = "http://***************:3141/zyx/pypi/+f/b20/c7c9a3bf70136/contourpy-1.3.3-cp313-cp313-win32.whl", hash = "sha256:b20c7c9a3bf701366556e1b1984ed2d0cedf999903c51311417cf5f591d8c78d" },
    { url = "http://***************:3141/zyx/pypi/+f/1ca/dd8b8969f060b/contourpy-1.3.3-cp313-cp313-win_amd64.whl", hash = "sha256:1cadd8b8969f060ba45ed7c1b714fe69185812ab43bd6b86a9123fe8f99c3263" },
    { url = "http://***************:3141/zyx/pypi/+f/fd9/14713266421b7/contourpy-1.3.3-cp313-cp313-win_arm64.whl", hash = "sha256:fd914713266421b7536de2bfa8181aa8c699432b6763a0ea64195ebe28bff6a9" },
    { url = "http://***************:3141/zyx/pypi/+f/88d/f9880d5071694/contourpy-1.3.3-cp313-cp313t-macosx_10_13_x86_64.whl", hash = "sha256:88df9880d507169449d434c293467418b9f6cbe82edd19284aa0409e7fdb933d" },
    { url = "http://***************:3141/zyx/pypi/+f/d06/bb1f751ba5d41/contourpy-1.3.3-cp313-cp313t-macosx_11_0_arm64.whl", hash = "sha256:d06bb1f751ba5d417047db62bca3c8fde202b8c11fb50742ab3ab962c81e8216" },
    { url = "http://***************:3141/zyx/pypi/+f/e4e/6b05a45525357/contourpy-1.3.3-cp313-cp313t-manylinux_2_26_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:e4e6b05a45525357e382909a4c1600444e2a45b4795163d3b22669285591c1ae" },
    { url = "http://***************:3141/zyx/pypi/+f/ab3/074b48c4e2cf1/contourpy-1.3.3-cp313-cp313t-manylinux_2_26_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:ab3074b48c4e2cf1a960e6bbeb7f04566bf36b1861d5c9d4d8ac04b82e38ba20" },
    { url = "http://***************:3141/zyx/pypi/+f/6c3/d53c796f8647d/contourpy-1.3.3-cp313-cp313t-manylinux_2_26_s390x.manylinux_2_28_s390x.whl", hash = "sha256:6c3d53c796f8647d6deb1abe867daeb66dcc8a97e8455efa729516b997b8ed99" },
    { url = "http://***************:3141/zyx/pypi/+f/50e/d930df7289ff2/contourpy-1.3.3-cp313-cp313t-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:50ed930df7289ff2a8d7afeb9603f8289e5704755c7e5c3bbd929c90c817164b" },
    { url = "http://***************:3141/zyx/pypi/+f/4fe/ffb6537d64b84/contourpy-1.3.3-cp313-cp313t-musllinux_1_2_aarch64.whl", hash = "sha256:4feffb6537d64b84877da813a5c30f1422ea5739566abf0bd18065ac040e120a" },
    { url = "http://***************:3141/zyx/pypi/+f/2b7/e9480ffe2b0cd/contourpy-1.3.3-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:2b7e9480ffe2b0cd2e787e4df64270e3a0440d9db8dc823312e2c940c167df7e" },
    { url = "http://***************:3141/zyx/pypi/+f/283/edd842a01e3dc/contourpy-1.3.3-cp313-cp313t-win32.whl", hash = "sha256:283edd842a01e3dcd435b1c5116798d661378d83d36d337b8dde1d16a5fc9ba3" },
    { url = "http://***************:3141/zyx/pypi/+f/87a/cf5963fc2b348/contourpy-1.3.3-cp313-cp313t-win_amd64.whl", hash = "sha256:87acf5963fc2b34825e5b6b048f40e3635dd547f590b04d2ab317c2619ef7ae8" },
    { url = "http://***************:3141/zyx/pypi/+f/3c3/0273eb2a55024/contourpy-1.3.3-cp313-cp313t-win_arm64.whl", hash = "sha256:3c30273eb2a55024ff31ba7d052dde990d7d8e5450f4bbb6e913558b3d6c2301" },
    { url = "http://***************:3141/zyx/pypi/+f/fde/6c716d51c04b1/contourpy-1.3.3-cp314-cp314-macosx_10_13_x86_64.whl", hash = "sha256:fde6c716d51c04b1c25d0b90364d0be954624a0ee9d60e23e850e8d48353d07a" },
    { url = "http://***************:3141/zyx/pypi/+f/cbe/db772ed74ff5b/contourpy-1.3.3-cp314-cp314-macosx_11_0_arm64.whl", hash = "sha256:cbedb772ed74ff5be440fa8eee9bd49f64f6e3fc09436d9c7d8f1c287b121d77" },
    { url = "http://***************:3141/zyx/pypi/+f/22e/9b1bd7a9b1d65/contourpy-1.3.3-cp314-cp314-manylinux_2_26_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:22e9b1bd7a9b1d652cd77388465dc358dafcd2e217d35552424aa4f996f524f5" },
    { url = "http://***************:3141/zyx/pypi/+f/a22/738912262aa3e/contourpy-1.3.3-cp314-cp314-manylinux_2_26_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:a22738912262aa3e254e4f3cb079a95a67132fc5a063890e224393596902f5a4" },
    { url = "http://***************:3141/zyx/pypi/+f/afe/5a512f31ee6bd/contourpy-1.3.3-cp314-cp314-manylinux_2_26_s390x.manylinux_2_28_s390x.whl", hash = "sha256:afe5a512f31ee6bd7d0dda52ec9864c984ca3d66664444f2d72e0dc4eb832e36" },
    { url = "http://***************:3141/zyx/pypi/+f/f64/836de09927cba/contourpy-1.3.3-cp314-cp314-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:f64836de09927cba6f79dcd00fdd7d5329f3fccc633468507079c829ca4db4e3" },
    { url = "http://***************:3141/zyx/pypi/+f/1fd/43c3be4c8e5fd/contourpy-1.3.3-cp314-cp314-musllinux_1_2_aarch64.whl", hash = "sha256:1fd43c3be4c8e5fd6e4f2baeae35ae18176cf2e5cced681cca908addf1cdd53b" },
    { url = "http://***************:3141/zyx/pypi/+f/6af/c576f7b33cf00/contourpy-1.3.3-cp314-cp314-musllinux_1_2_x86_64.whl", hash = "sha256:6afc576f7b33cf00996e5c1102dc2a8f7cc89e39c0b55df93a0b78c1bd992b36" },
    { url = "http://***************:3141/zyx/pypi/+f/66c/8a43a4f7b8df8/contourpy-1.3.3-cp314-cp314-win32.whl", hash = "sha256:66c8a43a4f7b8df8b71ee1840e4211a3c8d93b214b213f590e18a1beca458f7d" },
    { url = "http://***************:3141/zyx/pypi/+f/cf9/022ef053f2694/contourpy-1.3.3-cp314-cp314-win_amd64.whl", hash = "sha256:cf9022ef053f2694e31d630feaacb21ea24224be1c3ad0520b13d844274614fd" },
    { url = "http://***************:3141/zyx/pypi/+f/95b/181891b4c71de/contourpy-1.3.3-cp314-cp314-win_arm64.whl", hash = "sha256:95b181891b4c71de4bb404c6621e7e2390745f887f2a026b2d99e92c17892339" },
    { url = "http://***************:3141/zyx/pypi/+f/33c/82d0138c0a062/contourpy-1.3.3-cp314-cp314t-macosx_10_13_x86_64.whl", hash = "sha256:33c82d0138c0a062380332c861387650c82e4cf1747aaa6938b9b6516762e772" },
    { url = "http://***************:3141/zyx/pypi/+f/ea3/7e7b45949df43/contourpy-1.3.3-cp314-cp314t-macosx_11_0_arm64.whl", hash = "sha256:ea37e7b45949df430fe649e5de8351c423430046a2af20b1c1961cae3afcda77" },
    { url = "http://***************:3141/zyx/pypi/+f/d30/4906ecc71672e/contourpy-1.3.3-cp314-cp314t-manylinux_2_26_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:d304906ecc71672e9c89e87c4675dc5c2645e1f4269a5063b99b0bb29f232d13" },
    { url = "http://***************:3141/zyx/pypi/+f/ca6/58cd1a680a5c9/contourpy-1.3.3-cp314-cp314t-manylinux_2_26_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:ca658cd1a680a5c9ea96dc61cdbae1e85c8f25849843aa799dfd3cb370ad4fbe" },
    { url = "http://***************:3141/zyx/pypi/+f/ab2/fd90904c50373/contourpy-1.3.3-cp314-cp314t-manylinux_2_26_s390x.manylinux_2_28_s390x.whl", hash = "sha256:ab2fd90904c503739a75b7c8c5c01160130ba67944a7b77bbf36ef8054576e7f" },
    { url = "http://***************:3141/zyx/pypi/+f/b73/01b89040075c3/contourpy-1.3.3-cp314-cp314t-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:b7301b89040075c30e5768810bc96a8e8d78085b47d8be6e4c3f5a0b4ed478a0" },
    { url = "http://***************:3141/zyx/pypi/+f/2a2/a8b627d5cc6b7/contourpy-1.3.3-cp314-cp314t-musllinux_1_2_aarch64.whl", hash = "sha256:2a2a8b627d5cc6b7c41a4beff6c5ad5eb848c88255fda4a8745f7e901b32d8e4" },
    { url = "http://***************:3141/zyx/pypi/+f/fd6/ec6be509c787f/contourpy-1.3.3-cp314-cp314t-musllinux_1_2_x86_64.whl", hash = "sha256:fd6ec6be509c787f1caf6b247f0b1ca598bef13f4ddeaa126b7658215529ba0f" },
    { url = "http://***************:3141/zyx/pypi/+f/e74/a9a0f5e3fff48/contourpy-1.3.3-cp314-cp314t-win32.whl", hash = "sha256:e74a9a0f5e3fff48fb5a7f2fd2b9b70a3fe014a67522f79b7cca4c0c7e43c9ae" },
    { url = "http://***************:3141/zyx/pypi/+f/13b/68d6a62db8eaf/contourpy-1.3.3-cp314-cp314t-win_amd64.whl", hash = "sha256:13b68d6a62db8eafaebb8039218921399baf6e47bf85006fd8529f2a08ef33fc" },
    { url = "http://***************:3141/zyx/pypi/+f/b74/48cb5a725bb1e/contourpy-1.3.3-cp314-cp314t-win_arm64.whl", hash = "sha256:b7448cb5a725bb1e35ce88771b86fba35ef418952474492cf7c764059933ff8b" },
    { url = "http://***************:3141/zyx/pypi/+f/cd5/dfcaeb10f7b7f/contourpy-1.3.3-pp311-pypy311_pp73-macosx_10_15_x86_64.whl", hash = "sha256:cd5dfcaeb10f7b7f9dc8941717c6c2ade08f587be2226222c12b25f0483ed497" },
    { url = "http://***************:3141/zyx/pypi/+f/0c1/fc238306b35f2/contourpy-1.3.3-pp311-pypy311_pp73-macosx_11_0_arm64.whl", hash = "sha256:0c1fc238306b35f246d61a1d416a627348b5cf0648648a031e14bb8705fcdfe8" },
    { url = "http://***************:3141/zyx/pypi/+f/70f/9aad7de812d65/contourpy-1.3.3-pp311-pypy311_pp73-manylinux_2_26_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:70f9aad7de812d6541d29d2bbf8feb22ff7e1c299523db288004e3157ff4674e" },
    { url = "http://***************:3141/zyx/pypi/+f/5ed/3657edf08512f/contourpy-1.3.3-pp311-pypy311_pp73-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:5ed3657edf08512fc3fe81b510e35c2012fbd3081d2e26160f27ca28affec989" },
    { url = "http://***************:3141/zyx/pypi/+f/3d1/a3799d62d45c1/contourpy-1.3.3-pp311-pypy311_pp73-win_amd64.whl", hash = "sha256:3d1a3799d62d45c18bafd41c5fa05120b96a28079f2393af559b843d1a966a77" },
]

[[package]]
name = "cycler"
version = "0.12.1"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
sdist = { url = "http://***************:3141/zyx/pypi/+f/88b/b128f02ba341d/cycler-0.12.1.tar.gz", hash = "sha256:88bb128f02ba341da8ef447245a9e138fae777f6a23943da4540077d3601eb1c" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/85c/ef7cff222d864/cycler-0.12.1-py3-none-any.whl", hash = "sha256:85cef7cff222d8644161529808465972e51340599459b8ac3ccbac5a854e0d30" },
]

[[package]]
name = "fonttools"
version = "4.59.1"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
sdist = { url = "http://***************:3141/zyx/pypi/+f/749/95b402ad09822/fonttools-4.59.1.tar.gz", hash = "sha256:74995b402ad09822a4c8002438e54940d9f1ecda898d2bb057729d7da983e4cb" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/490/9cce2e35706f3/fonttools-4.59.1-cp311-cp311-macosx_10_9_universal2.whl", hash = "sha256:4909cce2e35706f3d18c54d3dcce0414ba5e0fb436a454dffec459c61653b513" },
    { url = "http://***************:3141/zyx/pypi/+f/efb/ec204fa9f8776/fonttools-4.59.1-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:efbec204fa9f877641747f2d9612b2b656071390d7a7ef07a9dbf0ecf9c7195c" },
    { url = "http://***************:3141/zyx/pypi/+f/39d/fd42cc2dc647b/fonttools-4.59.1-cp311-cp311-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:39dfd42cc2dc647b2c5469bc7a5b234d9a49e72565b96dd14ae6f11c2c59ef15" },
    { url = "http://***************:3141/zyx/pypi/+f/b11/bc177a0d428b3/fonttools-4.59.1-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:b11bc177a0d428b37890825d7d025040d591aa833f85f8d8878ed183354f47df" },
    { url = "http://***************:3141/zyx/pypi/+f/5b9/b4c35b3be45e5/fonttools-4.59.1-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:5b9b4c35b3be45e5bc774d3fc9608bbf4f9a8d371103b858c80edbeed31dd5aa" },
    { url = "http://***************:3141/zyx/pypi/+f/011/58376b8a418a0/fonttools-4.59.1-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:01158376b8a418a0bae9625c476cebfcfcb5e6761e9d243b219cd58341e7afbb" },
    { url = "http://***************:3141/zyx/pypi/+f/cf7/c5089d3778738/fonttools-4.59.1-cp311-cp311-win32.whl", hash = "sha256:cf7c5089d37787387123f1cb8f1793a47c5e1e3d1e4e7bfbc1cc96e0f925eabe" },
    { url = "http://***************:3141/zyx/pypi/+f/c86/6eef7a0ba3204/fonttools-4.59.1-cp311-cp311-win_amd64.whl", hash = "sha256:c866eef7a0ba320486ade6c32bfc12813d1a5db8567e6904fb56d3d40acc5116" },
    { url = "http://***************:3141/zyx/pypi/+f/43a/b814bbba5f02a/fonttools-4.59.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:43ab814bbba5f02a93a152ee61a04182bb5809bd2bc3609f7822e12c53ae2c91" },
    { url = "http://***************:3141/zyx/pypi/+f/4f0/4c3ffbfa0baaf/fonttools-4.59.1-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:4f04c3ffbfa0baafcbc550657cf83657034eb63304d27b05cff1653b448ccff6" },
    { url = "http://***************:3141/zyx/pypi/+f/d60/1b153e51a5a62/fonttools-4.59.1-cp312-cp312-manylinux1_x86_64.manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_5_x86_64.whl", hash = "sha256:d601b153e51a5a6221f0d4ec077b6bfc6ac35bfe6c19aeaa233d8990b2b71726" },
    { url = "http://***************:3141/zyx/pypi/+f/c73/5e385e30278c5/fonttools-4.59.1-cp312-cp312-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:c735e385e30278c54f43a0d056736942023c9043f84ee1021eff9fd616d17693" },
    { url = "http://***************:3141/zyx/pypi/+f/101/7413cdc8555dc/fonttools-4.59.1-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:1017413cdc8555dce7ee23720da490282ab7ec1cf022af90a241f33f9a49afc4" },
    { url = "http://***************:3141/zyx/pypi/+f/5c6/d8d773470a510/fonttools-4.59.1-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:5c6d8d773470a5107052874341ed3c487c16ecd179976d81afed89dea5cd7406" },
    { url = "http://***************:3141/zyx/pypi/+f/2a2/d0d33307f6ad3/fonttools-4.59.1-cp312-cp312-win32.whl", hash = "sha256:2a2d0d33307f6ad3a2086a95dd607c202ea8852fa9fb52af9b48811154d1428a" },
    { url = "http://***************:3141/zyx/pypi/+f/0b9/e4fa7eaf046ed/fonttools-4.59.1-cp312-cp312-win_amd64.whl", hash = "sha256:0b9e4fa7eaf046ed6ac470f6033d52c052481ff7a6e0a92373d14f556f298dc0" },
    { url = "http://***************:3141/zyx/pypi/+f/89d/9957b54246c62/fonttools-4.59.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:89d9957b54246c6251345297dddf77a84d2c19df96af30d2de24093bbdf0528b" },
    { url = "http://***************:3141/zyx/pypi/+f/815/6b11c0d540581/fonttools-4.59.1-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:8156b11c0d5405810d216f53907bd0f8b982aa5f1e7e3127ab3be1a4062154ff" },
    { url = "http://***************:3141/zyx/pypi/+f/838/7876a8011caec/fonttools-4.59.1-cp313-cp313-manylinux1_x86_64.manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_5_x86_64.whl", hash = "sha256:8387876a8011caec52d327d5e5bca705d9399ec4b17afb8b431ec50d47c17d23" },
    { url = "http://***************:3141/zyx/pypi/+f/fb1/3823a74b3a920/fonttools-4.59.1-cp313-cp313-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:fb13823a74b3a9204a8ed76d3d6d5ec12e64cc5bc44914eb9ff1cdac04facd43" },
    { url = "http://***************:3141/zyx/pypi/+f/e1c/a10da138c300f/fonttools-4.59.1-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:e1ca10da138c300f768bb68e40e5b20b6ecfbd95f91aac4cc15010b6b9d65455" },
    { url = "http://***************:3141/zyx/pypi/+f/2be/b5bfc4887a313/fonttools-4.59.1-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:2beb5bfc4887a3130f8625349605a3a45fe345655ce6031d1bac11017454b943" },
    { url = "http://***************:3141/zyx/pypi/+f/419/f16d750d78e6d/fonttools-4.59.1-cp313-cp313-win32.whl", hash = "sha256:419f16d750d78e6d704bfe97b48bba2f73b15c9418f817d0cb8a9ca87a5b94bf" },
    { url = "http://***************:3141/zyx/pypi/+f/c53/6f8a852e8d3fa/fonttools-4.59.1-cp313-cp313-win_amd64.whl", hash = "sha256:c536f8a852e8d3fa71dde1ec03892aee50be59f7154b533f0bf3c1174cfd5126" },
    { url = "http://***************:3141/zyx/pypi/+f/d5c/3bfdc9663f3d4/fonttools-4.59.1-cp314-cp314-macosx_10_13_universal2.whl", hash = "sha256:d5c3bfdc9663f3d4b565f9cb3b8c1efb3e178186435b45105bde7328cfddd7fe" },
    { url = "http://***************:3141/zyx/pypi/+f/ea0/3f1da0d722fe3/fonttools-4.59.1-cp314-cp314-macosx_10_13_x86_64.whl", hash = "sha256:ea03f1da0d722fe3c2278a05957e6550175571a4894fbf9d178ceef4a3783d2b" },
    { url = "http://***************:3141/zyx/pypi/+f/57a/3708ca6bfccb7/fonttools-4.59.1-cp314-cp314-manylinux1_x86_64.manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_5_x86_64.whl", hash = "sha256:57a3708ca6bfccb790f585fa6d8f29432ec329618a09ff94c16bcb3c55994643" },
    { url = "http://***************:3141/zyx/pypi/+f/729/367c91eb1ee84/fonttools-4.59.1-cp314-cp314-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:729367c91eb1ee84e61a733acc485065a00590618ca31c438e7dd4d600c01486" },
    { url = "http://***************:3141/zyx/pypi/+f/8f8/ef66ac6db4501/fonttools-4.59.1-cp314-cp314-musllinux_1_2_aarch64.whl", hash = "sha256:8f8ef66ac6db450193ed150e10b3b45dde7aded10c5d279968bc63368027f62b" },
    { url = "http://***************:3141/zyx/pypi/+f/075/f745d539a998c/fonttools-4.59.1-cp314-cp314-musllinux_1_2_x86_64.whl", hash = "sha256:075f745d539a998cd92cb84c339a82e53e49114ec62aaea8307c80d3ad3aef3a" },
    { url = "http://***************:3141/zyx/pypi/+f/c2b/0597522d4c5bb/fonttools-4.59.1-cp314-cp314-win32.whl", hash = "sha256:c2b0597522d4c5bb18aa5cf258746a2d4a90f25878cbe865e4d35526abd1b9fc" },
    { url = "http://***************:3141/zyx/pypi/+f/e9a/d4ce044e3236f/fonttools-4.59.1-cp314-cp314-win_amd64.whl", hash = "sha256:e9ad4ce044e3236f0814c906ccce8647046cc557539661e35211faadf76f283b" },
    { url = "http://***************:3141/zyx/pypi/+f/652/159e8214eb485/fonttools-4.59.1-cp314-cp314t-macosx_10_13_universal2.whl", hash = "sha256:652159e8214eb4856e8387ebcd6b6bd336ee258cbeb639c8be52005b122b9609" },
    { url = "http://***************:3141/zyx/pypi/+f/43d/177cd0e847ea0/fonttools-4.59.1-cp314-cp314t-macosx_10_13_x86_64.whl", hash = "sha256:43d177cd0e847ea026fedd9f099dc917da136ed8792d142298a252836390c478" },
    { url = "http://***************:3141/zyx/pypi/+f/e54/437651e1440ee/fonttools-4.59.1-cp314-cp314t-manylinux1_x86_64.manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_5_x86_64.whl", hash = "sha256:e54437651e1440ee53a95e6ceb6ee440b67a3d348c76f45f4f48de1a5ecab019" },
    { url = "http://***************:3141/zyx/pypi/+f/606/5fdec8ff44c32/fonttools-4.59.1-cp314-cp314t-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:6065fdec8ff44c32a483fd44abe5bcdb40dd5e2571a5034b555348f2b3a52cea" },
    { url = "http://***************:3141/zyx/pypi/+f/420/52b56d176f8b3/fonttools-4.59.1-cp314-cp314t-musllinux_1_2_aarch64.whl", hash = "sha256:42052b56d176f8b315fbc09259439c013c0cb2109df72447148aeda677599612" },
    { url = "http://***************:3141/zyx/pypi/+f/bcd/52eaa5c4c593a/fonttools-4.59.1-cp314-cp314t-musllinux_1_2_x86_64.whl", hash = "sha256:bcd52eaa5c4c593ae9f447c1d13e7e4a00ca21d755645efa660b6999425b3c88" },
    { url = "http://***************:3141/zyx/pypi/+f/02e/4fdf27c550dde/fonttools-4.59.1-cp314-cp314t-win32.whl", hash = "sha256:02e4fdf27c550dded10fe038a5981c29f81cb9bc649ff2eaa48e80dab8998f97" },
    { url = "http://***************:3141/zyx/pypi/+f/412/a5fd6345872a7/fonttools-4.59.1-cp314-cp314t-win_amd64.whl", hash = "sha256:412a5fd6345872a7c249dac5bcce380393f40c1c316ac07f447bc17d51900922" },
    { url = "http://***************:3141/zyx/pypi/+f/647/db657073672a8/fonttools-4.59.1-py3-none-any.whl", hash = "sha256:647db657073672a8330608970a984d51573557f328030566521bc03415535042" },
]

[[package]]
name = "h5py"
version = "3.14.0"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
dependencies = [
    { name = "numpy" },
]
sdist = { url = "http://***************:3141/zyx/pypi/+f/237/2116b2e0d5d3e/h5py-3.14.0.tar.gz", hash = "sha256:2372116b2e0d5d3e5e705b7f663f7c8d96fa79a4052d250484ef91d24d6a08f4" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/f30/dbc58f2a0efee/h5py-3.14.0-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:f30dbc58f2a0efeec6c8836c97f6c94afd769023f44e2bb0ed7b17a16ec46088" },
    { url = "http://***************:3141/zyx/pypi/+f/543/877d7f3d8f8a9/h5py-3.14.0-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:543877d7f3d8f8a9828ed5df6a0b78ca3d8846244b9702e99ed0d53610b583a8" },
    { url = "http://***************:3141/zyx/pypi/+f/8c4/97600c0496548/h5py-3.14.0-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:8c497600c0496548810047257e36360ff551df8b59156d3a4181072eed47d8ad" },
    { url = "http://***************:3141/zyx/pypi/+f/723/a40ee6505bd35/h5py-3.14.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:723a40ee6505bd354bfd26385f2dae7bbfa87655f4e61bab175a49d72ebfc06b" },
    { url = "http://***************:3141/zyx/pypi/+f/d27/44b520440a996/h5py-3.14.0-cp311-cp311-win_amd64.whl", hash = "sha256:d2744b520440a996f2dae97f901caa8a953afc055db4673a993f2d87d7f38713" },
    { url = "http://***************:3141/zyx/pypi/+f/e00/45115d8327209/h5py-3.14.0-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:e0045115d83272090b0717c555a31398c2c089b87d212ceba800d3dc5d952e23" },
    { url = "http://***************:3141/zyx/pypi/+f/6da/62509b7e1d71a/h5py-3.14.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:6da62509b7e1d71a7d110478aa25d245dd32c8d9a1daee9d2a42dba8717b047a" },
    { url = "http://***************:3141/zyx/pypi/+f/554/ef0ced3571366/h5py-3.14.0-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:554ef0ced3571366d4d383427c00c966c360e178b5fb5ee5bb31a435c424db0c" },
    { url = "http://***************:3141/zyx/pypi/+f/0cb/d41f4e3761f15/h5py-3.14.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:0cbd41f4e3761f150aa5b662df991868ca533872c95467216f2bec5fcad84882" },
    { url = "http://***************:3141/zyx/pypi/+f/bf4/897d67e613ecf/h5py-3.14.0-cp312-cp312-win_amd64.whl", hash = "sha256:bf4897d67e613ecf5bdfbdab39a1158a64df105827da70ea1d90243d796d367f" },
    { url = "http://***************:3141/zyx/pypi/+f/aa4/b7bbce683379b/h5py-3.14.0-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:aa4b7bbce683379b7bf80aaba68e17e23396100336a8d500206520052be2f812" },
    { url = "http://***************:3141/zyx/pypi/+f/ef9/603a501a04fcd/h5py-3.14.0-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:ef9603a501a04fcd0ba28dd8f0995303d26a77a980a1f9474b3417543d4c6174" },
    { url = "http://***************:3141/zyx/pypi/+f/e8c/baf6910fa3983/h5py-3.14.0-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:e8cbaf6910fa3983c46172666b0b8da7b7bd90d764399ca983236f2400436eeb" },
    { url = "http://***************:3141/zyx/pypi/+f/d90/e6445ab7c146d/h5py-3.14.0-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:d90e6445ab7c146d7f7981b11895d70bc1dd91278a4f9f9028bc0c95e4a53f13" },
    { url = "http://***************:3141/zyx/pypi/+f/ae1/8e3de237a7a83/h5py-3.14.0-cp313-cp313-win_amd64.whl", hash = "sha256:ae18e3de237a7a830adb76aaa68ad438d85fe6e19e0d99944a3ce46b772c69b3" },
]

[[package]]
name = "ionctrl-develop"
version = "0.1.0"
source = { virtual = "." }
dependencies = [
    { name = "artiq" },
]

[package.metadata]
requires-dist = [{ name = "artiq", specifier = ">=8.2.8" }]

[[package]]
name = "kiwisolver"
version = "1.4.9"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
sdist = { url = "http://***************:3141/zyx/pypi/+f/c3b/22c26c6fd6811/kiwisolver-1.4.9.tar.gz", hash = "sha256:c3b22c26c6fd6811b0ae8363b95ca8ce4ea3c202d3d0975b2914310ceb1bcc4d" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/eb1/4a5da6dc7642b/kiwisolver-1.4.9-cp311-cp311-macosx_10_9_universal2.whl", hash = "sha256:eb14a5da6dc7642b0f3a18f13654847cd8b7a2550e2645a5bda677862b03ba16" },
    { url = "http://***************:3141/zyx/pypi/+f/39a/219e1c81ae3b1/kiwisolver-1.4.9-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:39a219e1c81ae3b103643d2aedb90f1ef22650deb266ff12a19e7773f3e5f089" },
    { url = "http://***************:3141/zyx/pypi/+f/240/5a7d98604b87f/kiwisolver-1.4.9-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:2405a7d98604b87f3fc28b1716783534b1b4b8510d8142adca34ee0bc3c87543" },
    { url = "http://***************:3141/zyx/pypi/+f/dc1/ae486f9abcef2/kiwisolver-1.4.9-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:dc1ae486f9abcef254b5618dfb4113dd49f94c68e3e027d03cf0143f3f772b61" },
    { url = "http://***************:3141/zyx/pypi/+f/8a1/f570ce4d62d71/kiwisolver-1.4.9-cp311-cp311-manylinux_2_24_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:8a1f570ce4d62d718dce3f179ee78dac3b545ac16c0c04bb363b7607a949c0d1" },
    { url = "http://***************:3141/zyx/pypi/+f/cb2/7e7b78d716c59/kiwisolver-1.4.9-cp311-cp311-manylinux_2_24_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:cb27e7b78d716c591e88e0a09a2139c6577865d7f2e152488c2cc6257f460872" },
    { url = "http://***************:3141/zyx/pypi/+f/151/63165efc2f627/kiwisolver-1.4.9-cp311-cp311-manylinux_2_24_s390x.manylinux_2_28_s390x.whl", hash = "sha256:15163165efc2f627eb9687ea5f3a28137217d217ac4024893d753f46bce9de26" },
    { url = "http://***************:3141/zyx/pypi/+f/bde/e92c56a71d2b2/kiwisolver-1.4.9-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:bdee92c56a71d2b24c33a7d4c2856bd6419d017e08caa7802d2963870e315028" },
    { url = "http://***************:3141/zyx/pypi/+f/412/f287c55a6f54b/kiwisolver-1.4.9-cp311-cp311-musllinux_1_2_ppc64le.whl", hash = "sha256:412f287c55a6f54b0650bd9b6dce5aceddb95864a1a90c87af16979d37c89771" },
    { url = "http://***************:3141/zyx/pypi/+f/2c9/3f00dcba2eea7/kiwisolver-1.4.9-cp311-cp311-musllinux_1_2_s390x.whl", hash = "sha256:2c93f00dcba2eea70af2be5f11a830a742fe6b579a1d4e00f47760ef13be247a" },
    { url = "http://***************:3141/zyx/pypi/+f/f11/7e1a089d94116/kiwisolver-1.4.9-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:f117e1a089d9411663a3207ba874f31be9ac8eaa5b533787024dc07aeb74f464" },
    { url = "http://***************:3141/zyx/pypi/+f/be6/a04e6c79819c9/kiwisolver-1.4.9-cp311-cp311-win_amd64.whl", hash = "sha256:be6a04e6c79819c9a8c2373317d19a96048e5a3f90bec587787e86a1153883c2" },
    { url = "http://***************:3141/zyx/pypi/+f/0ae/37737256ba2de/kiwisolver-1.4.9-cp311-cp311-win_arm64.whl", hash = "sha256:0ae37737256ba2de764ddc12aed4956460277f00c4996d51a197e72f62f5eec7" },
    { url = "http://***************:3141/zyx/pypi/+f/ac5/a486ac389dddc/kiwisolver-1.4.9-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:ac5a486ac389dddcc5bef4f365b6ae3ffff2c433324fb38dd35e3fab7c957999" },
    { url = "http://***************:3141/zyx/pypi/+f/f2b/a92255faa7309/kiwisolver-1.4.9-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:f2ba92255faa7309d06fe44c3a4a97efe1c8d640c2a79a5ef728b685762a6fd2" },
    { url = "http://***************:3141/zyx/pypi/+f/4a2/899935e724dd1/kiwisolver-1.4.9-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:4a2899935e724dd1074cb568ce7ac0dce28b2cd6ab539c8e001a8578eb106d14" },
    { url = "http://***************:3141/zyx/pypi/+f/f60/08a4919fdbc0b/kiwisolver-1.4.9-cp312-cp312-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:f6008a4919fdbc0b0097089f67a1eb55d950ed7e90ce2cc3e640abadd2757a04" },
    { url = "http://***************:3141/zyx/pypi/+f/67b/b8b474b418177/kiwisolver-1.4.9-cp312-cp312-manylinux_2_24_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:67bb8b474b4181770f926f7b7d2f8c0248cbcb78b660fdd41a47054b28d2a752" },
    { url = "http://***************:3141/zyx/pypi/+f/232/7a4a30d3ee07d/kiwisolver-1.4.9-cp312-cp312-manylinux_2_24_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:2327a4a30d3ee07d2fbe2e7933e8a37c591663b96ce42a00bc67461a87d7df77" },
    { url = "http://***************:3141/zyx/pypi/+f/7a0/8b491ec91b1d5/kiwisolver-1.4.9-cp312-cp312-manylinux_2_24_s390x.manylinux_2_28_s390x.whl", hash = "sha256:7a08b491ec91b1d5053ac177afe5290adacf1f0f6307d771ccac5de30592d198" },
    { url = "http://***************:3141/zyx/pypi/+f/d8f/c5c867c22b828/kiwisolver-1.4.9-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:d8fc5c867c22b828001b6a38d2eaeb88160bf5783c6cb4a5e440efc981ce286d" },
    { url = "http://***************:3141/zyx/pypi/+f/3b3/115b2581ea35b/kiwisolver-1.4.9-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:3b3115b2581ea35bb6d1f24a4c90af37e5d9b49dcff267eeed14c3893c5b86ab" },
    { url = "http://***************:3141/zyx/pypi/+f/858/e4c22fb075920/kiwisolver-1.4.9-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:858e4c22fb075920b96a291928cb7dea5644e94c0ee4fcd5af7e865655e4ccf2" },
    { url = "http://***************:3141/zyx/pypi/+f/ed0/fecd28cc62c54/kiwisolver-1.4.9-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:ed0fecd28cc62c54b262e3736f8bb2512d8dcfdc2bcf08be5f47f96bf405b145" },
    { url = "http://***************:3141/zyx/pypi/+f/f68/208a520c3d86e/kiwisolver-1.4.9-cp312-cp312-win_amd64.whl", hash = "sha256:f68208a520c3d86ea51acf688a3e3002615a7f0238002cccc17affecc86a8a54" },
    { url = "http://***************:3141/zyx/pypi/+f/2c1/a4f57df73965f/kiwisolver-1.4.9-cp312-cp312-win_arm64.whl", hash = "sha256:2c1a4f57df73965f3f14df20b80ee29e6a7930a57d2d9e8491a25f676e197c60" },
    { url = "http://***************:3141/zyx/pypi/+f/a5d/0432ccf1c7ab1/kiwisolver-1.4.9-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:a5d0432ccf1c7ab14f9949eec60c5d1f924f17c037e9f8b33352fa05799359b8" },
    { url = "http://***************:3141/zyx/pypi/+f/efb/3a45b35622bb6/kiwisolver-1.4.9-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:efb3a45b35622bb6c16dbfab491a8f5a391fe0e9d45ef32f4df85658232ca0e2" },
    { url = "http://***************:3141/zyx/pypi/+f/1a1/2cf6398e8a0a0/kiwisolver-1.4.9-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:1a12cf6398e8a0a001a059747a1cbf24705e18fe413bc22de7b3d15c67cffe3f" },
    { url = "http://***************:3141/zyx/pypi/+f/b67/e6efbf68e077d/kiwisolver-1.4.9-cp313-cp313-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:b67e6efbf68e077dd71d1a6b37e43e1a99d0bff1a3d51867d45ee8908b931098" },
    { url = "http://***************:3141/zyx/pypi/+f/565/6aa670507437a/kiwisolver-1.4.9-cp313-cp313-manylinux_2_24_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:5656aa670507437af0207645273ccdfee4f14bacd7f7c67a4306d0dcaeaf6eed" },
    { url = "http://***************:3141/zyx/pypi/+f/bfc/08add55815534/kiwisolver-1.4.9-cp313-cp313-manylinux_2_24_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:bfc08add558155345129c7803b3671cf195e6a56e7a12f3dde7c57d9b417f525" },
    { url = "http://***************:3141/zyx/pypi/+f/400/92754720b174e/kiwisolver-1.4.9-cp313-cp313-manylinux_2_24_s390x.manylinux_2_28_s390x.whl", hash = "sha256:40092754720b174e6ccf9e845d0d8c7d8e12c3d71e7fc35f55f3813e96376f78" },
    { url = "http://***************:3141/zyx/pypi/+f/497/d05f29a1300d1/kiwisolver-1.4.9-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:497d05f29a1300d14e02e6441cf0f5ee81c1ff5a304b0d9fb77423974684e08b" },
    { url = "http://***************:3141/zyx/pypi/+f/bdd/1a81a1860476e/kiwisolver-1.4.9-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:bdd1a81a1860476eb41ac4bc1e07b3f07259e6d55bbf739b79c8aaedcf512799" },
    { url = "http://***************:3141/zyx/pypi/+f/e6b/93f13371d341a/kiwisolver-1.4.9-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:e6b93f13371d341afee3be9f7c5964e3fe61d5fa30f6a30eb49856935dfe4fc3" },
    { url = "http://***************:3141/zyx/pypi/+f/d75/aa530ccfaa593/kiwisolver-1.4.9-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:d75aa530ccfaa593da12834b86a0724f58bff12706659baa9227c2ccaa06264c" },
    { url = "http://***************:3141/zyx/pypi/+f/dd0/a578400839256/kiwisolver-1.4.9-cp313-cp313-win_amd64.whl", hash = "sha256:dd0a578400839256df88c16abddf9ba14813ec5f21362e1fe65022e00c883d4d" },
    { url = "http://***************:3141/zyx/pypi/+f/d41/88e73af84ca82/kiwisolver-1.4.9-cp313-cp313-win_arm64.whl", hash = "sha256:d4188e73af84ca82468f09cadc5ac4db578109e52acb4518d8154698d3a87ca2" },
    { url = "http://***************:3141/zyx/pypi/+f/5a0/f2724dfd4e3b3/kiwisolver-1.4.9-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:5a0f2724dfd4e3b3ac5a82436a8e6fd16baa7d507117e4279b660fe8ca38a3a1" },
    { url = "http://***************:3141/zyx/pypi/+f/1b1/1d6a633e4ed84/kiwisolver-1.4.9-cp313-cp313t-macosx_10_13_x86_64.whl", hash = "sha256:1b11d6a633e4ed84fc0ddafd4ebfd8ea49b3f25082c04ad12b8315c11d504dc1" },
    { url = "http://***************:3141/zyx/pypi/+f/618/74cdb0a360163/kiwisolver-1.4.9-cp313-cp313t-macosx_11_0_arm64.whl", hash = "sha256:61874cdb0a36016354853593cffc38e56fc9ca5aa97d2c05d3dcf6922cd55a11" },
    { url = "http://***************:3141/zyx/pypi/+f/60c/439763a969a6a/kiwisolver-1.4.9-cp313-cp313t-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:60c439763a969a6af93b4881db0eed8fadf93ee98e18cbc35bc8da868d0c4f0c" },
    { url = "http://***************:3141/zyx/pypi/+f/92a/2f997387a1b79/kiwisolver-1.4.9-cp313-cp313t-manylinux_2_24_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:92a2f997387a1b79a75e7803aa7ded2cfbe2823852ccf1ba3bcf613b62ae3197" },
    { url = "http://***************:3141/zyx/pypi/+f/a31/d512c812daea6/kiwisolver-1.4.9-cp313-cp313t-manylinux_2_24_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:a31d512c812daea6d8b3be3b2bfcbeb091dbb09177706569bcfc6240dcf8b41c" },
    { url = "http://***************:3141/zyx/pypi/+f/52a/15b0f35dad398/kiwisolver-1.4.9-cp313-cp313t-manylinux_2_24_s390x.manylinux_2_28_s390x.whl", hash = "sha256:52a15b0f35dad39862d376df10c5230155243a2c1a436e39eb55623ccbd68185" },
    { url = "http://***************:3141/zyx/pypi/+f/a30/fd6fdef1430fd/kiwisolver-1.4.9-cp313-cp313t-musllinux_1_2_aarch64.whl", hash = "sha256:a30fd6fdef1430fd9e1ba7b3398b5ee4e2887783917a687d86ba69985fb08748" },
    { url = "http://***************:3141/zyx/pypi/+f/cc9/617b46837c646/kiwisolver-1.4.9-cp313-cp313t-musllinux_1_2_ppc64le.whl", hash = "sha256:cc9617b46837c6468197b5945e196ee9ca43057bb7d9d1ae688101e4e1dddf64" },
    { url = "http://***************:3141/zyx/pypi/+f/0ab/74e19f6a2b027/kiwisolver-1.4.9-cp313-cp313t-musllinux_1_2_s390x.whl", hash = "sha256:0ab74e19f6a2b027ea4f845a78827969af45ce790e6cb3e1ebab71bdf9f215ff" },
    { url = "http://***************:3141/zyx/pypi/+f/dba/5ee5d3981160c/kiwisolver-1.4.9-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:dba5ee5d3981160c28d5490f0d1b7ed730c22470ff7f6cc26cfcfaacb9896a07" },
    { url = "http://***************:3141/zyx/pypi/+f/074/9fd8f4218ad2e/kiwisolver-1.4.9-cp313-cp313t-win_arm64.whl", hash = "sha256:0749fd8f4218ad2e851e11cc4dc05c7cbc0cbc4267bdfdb31782e65aace4ee9c" },
    { url = "http://***************:3141/zyx/pypi/+f/992/8fe1eb816d11a/kiwisolver-1.4.9-cp314-cp314-macosx_10_13_universal2.whl", hash = "sha256:9928fe1eb816d11ae170885a74d074f57af3a0d65777ca47e9aeb854a1fba386" },
    { url = "http://***************:3141/zyx/pypi/+f/d00/05b053977e7b4/kiwisolver-1.4.9-cp314-cp314-macosx_10_13_x86_64.whl", hash = "sha256:d0005b053977e7b43388ddec89fa567f43d4f6d5c2c0affe57de5ebf290dc552" },
    { url = "http://***************:3141/zyx/pypi/+f/263/5d352d67458b6/kiwisolver-1.4.9-cp314-cp314-macosx_11_0_arm64.whl", hash = "sha256:2635d352d67458b66fd0667c14cb1d4145e9560d503219034a18a87e971ce4f3" },
    { url = "http://***************:3141/zyx/pypi/+f/767/c23ad1c58c9e8/kiwisolver-1.4.9-cp314-cp314-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:767c23ad1c58c9e827b649a9ab7809fd5fd9db266a9cf02b0e926ddc2c680d58" },
    { url = "http://***************:3141/zyx/pypi/+f/72d/0eb9fba308b83/kiwisolver-1.4.9-cp314-cp314-manylinux_2_24_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:72d0eb9fba308b8311685c2268cf7d0a0639a6cd027d8128659f72bdd8a024b4" },
    { url = "http://***************:3141/zyx/pypi/+f/f68/e4f3eeca8fb22/kiwisolver-1.4.9-cp314-cp314-manylinux_2_24_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:f68e4f3eeca8fb22cc3d731f9715a13b652795ef657a13df1ad0c7dc0e9731df" },
    { url = "http://***************:3141/zyx/pypi/+f/d84/cd4061ae292d8/kiwisolver-1.4.9-cp314-cp314-manylinux_2_24_s390x.manylinux_2_28_s390x.whl", hash = "sha256:d84cd4061ae292d8ac367b2c3fa3aad11cb8625a95d135fe93f286f914f3f5a6" },
    { url = "http://***************:3141/zyx/pypi/+f/a60/ea74330b91bd2/kiwisolver-1.4.9-cp314-cp314-musllinux_1_2_aarch64.whl", hash = "sha256:a60ea74330b91bd22a29638940d115df9dc00af5035a9a2a6ad9399ffb4ceca5" },
    { url = "http://***************:3141/zyx/pypi/+f/ce6/a3a4e106cf35c/kiwisolver-1.4.9-cp314-cp314-musllinux_1_2_ppc64le.whl", hash = "sha256:ce6a3a4e106cf35c2d9c4fa17c05ce0b180db622736845d4315519397a77beaf" },
    { url = "http://***************:3141/zyx/pypi/+f/779/37e5e2a38a7b4/kiwisolver-1.4.9-cp314-cp314-musllinux_1_2_s390x.whl", hash = "sha256:77937e5e2a38a7b48eef0585114fe7930346993a88060d0bf886086d2aa49ef5" },
    { url = "http://***************:3141/zyx/pypi/+f/24c/175051354f4a2/kiwisolver-1.4.9-cp314-cp314-musllinux_1_2_x86_64.whl", hash = "sha256:24c175051354f4a28c5d6a31c93906dc653e2bf234e8a4bbfb964892078898ce" },
    { url = "http://***************:3141/zyx/pypi/+f/076/3515d4df10edf/kiwisolver-1.4.9-cp314-cp314-win_amd64.whl", hash = "sha256:0763515d4df10edf6d06a3c19734e2566368980d21ebec439f33f9eb936c07b7" },
    { url = "http://***************:3141/zyx/pypi/+f/0e4/e2bf29574a6a7/kiwisolver-1.4.9-cp314-cp314-win_arm64.whl", hash = "sha256:0e4e2bf29574a6a7b7f6cb5fa69293b9f96c928949ac4a53ba3f525dffb87f9c" },
    { url = "http://***************:3141/zyx/pypi/+f/d97/6bbb382b202f7/kiwisolver-1.4.9-cp314-cp314t-macosx_10_13_universal2.whl", hash = "sha256:d976bbb382b202f71c67f77b0ac11244021cfa3f7dfd9e562eefcea2df711548" },
    { url = "http://***************:3141/zyx/pypi/+f/248/9e4e5d7ef9a1c/kiwisolver-1.4.9-cp314-cp314t-macosx_10_13_x86_64.whl", hash = "sha256:2489e4e5d7ef9a1c300a5e0196e43d9c739f066ef23270607d45aba368b91f2d" },
    { url = "http://***************:3141/zyx/pypi/+f/e2e/a9f7ab7fbf18f/kiwisolver-1.4.9-cp314-cp314t-macosx_11_0_arm64.whl", hash = "sha256:e2ea9f7ab7fbf18fffb1b5434ce7c69a07582f7acc7717720f1d69f3e806f90c" },
    { url = "http://***************:3141/zyx/pypi/+f/b34/e51affded8fae/kiwisolver-1.4.9-cp314-cp314t-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:b34e51affded8faee0dfdb705416153819d8ea9250bbbf7ea1b249bdeb5f1122" },
    { url = "http://***************:3141/zyx/pypi/+f/d8a/acd3d4b33b772/kiwisolver-1.4.9-cp314-cp314t-manylinux_2_24_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:d8aacd3d4b33b772542b2e01beb50187536967b514b00003bdda7589722d2a64" },
    { url = "http://***************:3141/zyx/pypi/+f/7cf/974dd4e35fa31/kiwisolver-1.4.9-cp314-cp314t-manylinux_2_24_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:7cf974dd4e35fa315563ac99d6287a1024e4dc2077b8a7d7cd3d2fb65d283134" },
    { url = "http://***************:3141/zyx/pypi/+f/85b/d218b5ecfbee8/kiwisolver-1.4.9-cp314-cp314t-manylinux_2_24_s390x.manylinux_2_28_s390x.whl", hash = "sha256:85bd218b5ecfbee8c8a82e121802dcb519a86044c9c3b2e4aef02fa05c6da370" },
    { url = "http://***************:3141/zyx/pypi/+f/085/6e241c2d3df4e/kiwisolver-1.4.9-cp314-cp314t-musllinux_1_2_aarch64.whl", hash = "sha256:0856e241c2d3df4efef7c04a1e46b1936b6120c9bcf36dd216e3acd84bc4fb21" },
    { url = "http://***************:3141/zyx/pypi/+f/9af/39d6551f97d31/kiwisolver-1.4.9-cp314-cp314t-musllinux_1_2_ppc64le.whl", hash = "sha256:9af39d6551f97d31a4deebeac6f45b156f9755ddc59c07b402c148f5dbb6482a" },
    { url = "http://***************:3141/zyx/pypi/+f/bb4/ae2b57fc1d8cb/kiwisolver-1.4.9-cp314-cp314t-musllinux_1_2_s390x.whl", hash = "sha256:bb4ae2b57fc1d8cbd1cf7b1d9913803681ffa903e7488012be5b76dedf49297f" },
    { url = "http://***************:3141/zyx/pypi/+f/aed/ff62918805fb6/kiwisolver-1.4.9-cp314-cp314t-musllinux_1_2_x86_64.whl", hash = "sha256:aedff62918805fb62d43a4aa2ecd4482c380dc76cd31bd7c8878588a61bd0369" },
    { url = "http://***************:3141/zyx/pypi/+f/1fa/333e8b2ce4d96/kiwisolver-1.4.9-cp314-cp314t-win_amd64.whl", hash = "sha256:1fa333e8b2ce4d9660f2cda9c0e1b6bafcfb2457a9d259faa82289e73ec24891" },
    { url = "http://***************:3141/zyx/pypi/+f/4a4/8a2ce79d65d36/kiwisolver-1.4.9-cp314-cp314t-win_arm64.whl", hash = "sha256:4a48a2ce79d65d363597ef7b567ce3d14d68783d2b2263d98db3d9477805ba32" },
    { url = "http://***************:3141/zyx/pypi/+f/720/e05574713db64/kiwisolver-1.4.9-pp311-pypy311_pp73-macosx_10_15_x86_64.whl", hash = "sha256:720e05574713db64c356e86732c0f3c5252818d05f9df320f0ad8380641acea5" },
    { url = "http://***************:3141/zyx/pypi/+f/176/80d737d5335b5/kiwisolver-1.4.9-pp311-pypy311_pp73-macosx_11_0_arm64.whl", hash = "sha256:17680d737d5335b552994a2008fab4c851bcd7de33094a82067ef3a576ff02fa" },
    { url = "http://***************:3141/zyx/pypi/+f/85b/5352f94e490c0/kiwisolver-1.4.9-pp311-pypy311_pp73-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:85b5352f94e490c028926ea567fc569c52ec79ce131dadb968d3853e809518c2" },
    { url = "http://***************:3141/zyx/pypi/+f/464/415881e480129/kiwisolver-1.4.9-pp311-pypy311_pp73-manylinux_2_24_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:464415881e4801295659462c49461a24fb107c140de781d55518c4b80cb6790f" },
    { url = "http://***************:3141/zyx/pypi/+f/fb9/40820c63a9590/kiwisolver-1.4.9-pp311-pypy311_pp73-win_amd64.whl", hash = "sha256:fb940820c63a9590d31d88b815e7a3aa5915cad3ce735ab45f0c730b39547de1" },
]

[[package]]
name = "levenshtein"
version = "0.27.1"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
dependencies = [
    { name = "rapidfuzz" },
]
sdist = { url = "http://***************:3141/zyx/pypi/+f/3e1/8b73564cfc846/levenshtein-0.27.1.tar.gz", hash = "sha256:3e18b73564cfc846eec94dd13fab6cb006b5d2e0cc56bad1fd7d5585881302e3" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/2e6/f1760108319a1/levenshtein-0.27.1-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:2e6f1760108319a108dceb2f02bc7cdb78807ad1f9c673c95eaa1d0fe5dfcaae" },
    { url = "http://***************:3141/zyx/pypi/+f/c4e/d8400d94ab348/levenshtein-0.27.1-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:c4ed8400d94ab348099395e050b8ed9dd6a5d6b5b9e75e78b2b3d0b5f5b10f38" },
    { url = "http://***************:3141/zyx/pypi/+f/782/6efe51be8ff58/levenshtein-0.27.1-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:7826efe51be8ff58bc44a633e022fdd4b9fc07396375a6dbc4945a3bffc7bf8f" },
    { url = "http://***************:3141/zyx/pypi/+f/ff5/afb78719659d3/levenshtein-0.27.1-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:ff5afb78719659d353055863c7cb31599fbea6865c0890b2d840ee40214b3ddb" },
    { url = "http://***************:3141/zyx/pypi/+f/201/dafd5c004cd52/levenshtein-0.27.1-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:201dafd5c004cd52018560cf3213da799534d130cf0e4db839b51f3f06771de0" },
    { url = "http://***************:3141/zyx/pypi/+f/e5d/dd59f3cfaec21/levenshtein-0.27.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:e5ddd59f3cfaec216811ee67544779d9e2d6ed33f79337492a248245d6379e3d" },
    { url = "http://***************:3141/zyx/pypi/+f/6af/c241d27ecf5b9/levenshtein-0.27.1-cp311-cp311-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:6afc241d27ecf5b921063b796812c55b0115423ca6fa4827aa4b1581643d0a65" },
    { url = "http://***************:3141/zyx/pypi/+f/ee2/e766277cceb8c/levenshtein-0.27.1-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:ee2e766277cceb8ca9e584ea03b8dc064449ba588d3e24c1923e4b07576db574" },
    { url = "http://***************:3141/zyx/pypi/+f/920/b23d610945391/levenshtein-0.27.1-cp311-cp311-musllinux_1_2_i686.whl", hash = "sha256:920b23d6109453913ce78ec451bc402ff19d020ee8be4722e9d11192ec2fac6f" },
    { url = "http://***************:3141/zyx/pypi/+f/560/d7edba126e2ee/levenshtein-0.27.1-cp311-cp311-musllinux_1_2_ppc64le.whl", hash = "sha256:560d7edba126e2eea3ac3f2f12e7bd8bc9c6904089d12b5b23b6dfa98810b209" },
    { url = "http://***************:3141/zyx/pypi/+f/8d5/362b6c7aa4896/levenshtein-0.27.1-cp311-cp311-musllinux_1_2_s390x.whl", hash = "sha256:8d5362b6c7aa4896dc0cb1e7470a4ad3c06124e0af055dda30d81d3c5549346b" },
    { url = "http://***************:3141/zyx/pypi/+f/65b/a880815b0f80a/levenshtein-0.27.1-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:65ba880815b0f80a80a293aeebac0fab8069d03ad2d6f967a886063458f9d7a1" },
    { url = "http://***************:3141/zyx/pypi/+f/fcc/08effe77fec0b/levenshtein-0.27.1-cp311-cp311-win32.whl", hash = "sha256:fcc08effe77fec0bc5b0f6f10ff20b9802b961c4a69047b5499f383119ddbe24" },
    { url = "http://***************:3141/zyx/pypi/+f/0ed/402d8902be7df/levenshtein-0.27.1-cp311-cp311-win_amd64.whl", hash = "sha256:0ed402d8902be7df212ac598fc189f9b2d520817fdbc6a05e2ce44f7f3ef6857" },
    { url = "http://***************:3141/zyx/pypi/+f/7fd/aab29af81a8eb/levenshtein-0.27.1-cp311-cp311-win_arm64.whl", hash = "sha256:7fdaab29af81a8eb981043737f42450efca64b9761ca29385487b29c506da5b5" },
    { url = "http://***************:3141/zyx/pypi/+f/25f/b540d8c55d1dc/levenshtein-0.27.1-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:25fb540d8c55d1dc7bdc59b7de518ea5ed9df92eb2077e74bcb9bb6de7b06f69" },
    { url = "http://***************:3141/zyx/pypi/+f/f09/cfab6387e9c90/levenshtein-0.27.1-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:f09cfab6387e9c908c7b37961c045e8e10eb9b7ec4a700367f8e080ee803a562" },
    { url = "http://***************:3141/zyx/pypi/+f/daf/a29c0e616f322/levenshtein-0.27.1-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:dafa29c0e616f322b574e0b2aeb5b1ff2f8d9a1a6550f22321f3bd9bb81036e3" },
    { url = "http://***************:3141/zyx/pypi/+f/be7/a7642ea64392f/levenshtein-0.27.1-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:be7a7642ea64392fa1e6ef7968c2e50ef2152c60948f95d0793361ed97cf8a6f" },
    { url = "http://***************:3141/zyx/pypi/+f/060/b48c45ed54bce/levenshtein-0.27.1-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:060b48c45ed54bcea9582ce79c6365b20a1a7473767e0b3d6be712fa3a22929c" },
    { url = "http://***************:3141/zyx/pypi/+f/712/f562c5e64dd03/levenshtein-0.27.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:712f562c5e64dd0398d3570fe99f8fbb88acec7cc431f101cb66c9d22d74c542" },
    { url = "http://***************:3141/zyx/pypi/+f/a61/41ad65cab49aa/levenshtein-0.27.1-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:a6141ad65cab49aa4527a3342d76c30c48adb2393b6cdfeca65caae8d25cb4b8" },
    { url = "http://***************:3141/zyx/pypi/+f/799/b8d73cda32653/levenshtein-0.27.1-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:799b8d73cda3265331116f62932f553804eae16c706ceb35aaf16fc2a704791b" },
    { url = "http://***************:3141/zyx/pypi/+f/ec9/9871d98e517e1/levenshtein-0.27.1-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:ec99871d98e517e1cc4a15659c62d6ea63ee5a2d72c5ddbebd7bae8b9e2670c8" },
    { url = "http://***************:3141/zyx/pypi/+f/879/9164e1f83588d/levenshtein-0.27.1-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:8799164e1f83588dbdde07f728ea80796ea72196ea23484d78d891470241b222" },
    { url = "http://***************:3141/zyx/pypi/+f/583/9438138983265/levenshtein-0.27.1-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:583943813898326516ab451a83f734c6f07488cda5c361676150d3e3e8b47927" },
    { url = "http://***************:3141/zyx/pypi/+f/5bb/22956af44bb4e/levenshtein-0.27.1-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:5bb22956af44bb4eade93546bf95be610c8939b9a9d4d28b2dfa94abf454fed7" },
    { url = "http://***************:3141/zyx/pypi/+f/d90/99ed1bcfa7ccc/levenshtein-0.27.1-cp312-cp312-win32.whl", hash = "sha256:d9099ed1bcfa7ccc5540e8ad27b5dc6f23d16addcbe21fdd82af6440f4ed2b6d" },
    { url = "http://***************:3141/zyx/pypi/+f/7f0/71ecdb50aa6c1/levenshtein-0.27.1-cp312-cp312-win_amd64.whl", hash = "sha256:7f071ecdb50aa6c15fd8ae5bcb67e9da46ba1df7bba7c6bf6803a54c7a41fd96" },
    { url = "http://***************:3141/zyx/pypi/+f/83b/9033a984ccace/levenshtein-0.27.1-cp312-cp312-win_arm64.whl", hash = "sha256:83b9033a984ccace7703f35b688f3907d55490182fd39b33a8e434d7b2e249e6" },
    { url = "http://***************:3141/zyx/pypi/+f/ab0/0c2cae2889166/levenshtein-0.27.1-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:ab00c2cae2889166afb7e1af64af2d4e8c1b126f3902d13ef3740df00e54032d" },
    { url = "http://***************:3141/zyx/pypi/+f/c27/e00bc7527e282/levenshtein-0.27.1-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:c27e00bc7527e282f7c437817081df8da4eb7054e7ef9055b851fa3947896560" },
    { url = "http://***************:3141/zyx/pypi/+f/a5b/07de42bfc0511/levenshtein-0.27.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:a5b07de42bfc051136cc8e7f1e7ba2cb73666aa0429930f4218efabfdc5837ad" },
    { url = "http://***************:3141/zyx/pypi/+f/fb1/1ad3c9dae3063/levenshtein-0.27.1-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:fb11ad3c9dae3063405aa50d9c96923722ab17bb606c776b6817d70b51fd7e07" },
    { url = "http://***************:3141/zyx/pypi/+f/5c5/986fb46cb0c06/levenshtein-0.27.1-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:5c5986fb46cb0c063305fd45b0a79924abf2959a6d984bbac2b511d3ab259f3f" },
    { url = "http://***************:3141/zyx/pypi/+f/751/91e469269ddef/levenshtein-0.27.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:75191e469269ddef2859bc64c4a8cfd6c9e063302766b5cb7e1e67f38cc7051a" },
    { url = "http://***************:3141/zyx/pypi/+f/51b/3a7b2266933ba/levenshtein-0.27.1-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:51b3a7b2266933babc04e4d9821a495142eebd6ef709f90e24bc532b52b81385" },
    { url = "http://***************:3141/zyx/pypi/+f/bba/c509794afc3e2/levenshtein-0.27.1-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:bbac509794afc3e2a9e73284c9e3d0aab5b1d928643f42b172969c3eefa1f2a3" },
    { url = "http://***************:3141/zyx/pypi/+f/8d6/8714785178347/levenshtein-0.27.1-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:8d68714785178347ecb272b94e85cbf7e638165895c4dd17ab57e7742d8872ec" },
    { url = "http://***************:3141/zyx/pypi/+f/8ee/74ee31a5ab8f6/levenshtein-0.27.1-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:8ee74ee31a5ab8f61cd6c6c6e9ade4488dde1285f3c12207afc018393c9b8d14" },
    { url = "http://***************:3141/zyx/pypi/+f/f24/41b6365453ec8/levenshtein-0.27.1-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:f2441b6365453ec89640b85344afd3d602b0d9972840b693508074c613486ce7" },
    { url = "http://***************:3141/zyx/pypi/+f/a9b/e39640a46d8a0/levenshtein-0.27.1-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:a9be39640a46d8a0f9be729e641651d16a62b2c07d3f4468c36e1cc66b0183b9" },
    { url = "http://***************:3141/zyx/pypi/+f/a52/0af67d976761e/levenshtein-0.27.1-cp313-cp313-win32.whl", hash = "sha256:a520af67d976761eb6580e7c026a07eb8f74f910f17ce60e98d6e492a1f126c7" },
    { url = "http://***************:3141/zyx/pypi/+f/7dd/60aa49c2d8d23/levenshtein-0.27.1-cp313-cp313-win_amd64.whl", hash = "sha256:7dd60aa49c2d8d23e0ef6452c8329029f5d092f386a177e3385d315cabb78f2a" },
    { url = "http://***************:3141/zyx/pypi/+f/149/cd4f0baf5884a/levenshtein-0.27.1-cp313-cp313-win_arm64.whl", hash = "sha256:149cd4f0baf5884ac5df625b7b0d281721b15de00f447080e38f5188106e1167" },
    { url = "http://***************:3141/zyx/pypi/+f/909/b7b6bce27a4ec/levenshtein-0.27.1-pp311-pypy311_pp73-macosx_10_15_x86_64.whl", hash = "sha256:909b7b6bce27a4ec90576c9a9bd9af5a41308dfecf364b410e80b58038277bbe" },
    { url = "http://***************:3141/zyx/pypi/+f/d19/3a7f97b8c6a35/levenshtein-0.27.1-pp311-pypy311_pp73-macosx_11_0_arm64.whl", hash = "sha256:d193a7f97b8c6a350e36ec58e41a627c06fa4157c3ce4b2b11d90cfc3c2ebb8f" },
    { url = "http://***************:3141/zyx/pypi/+f/614/be316e3c06118/levenshtein-0.27.1-pp311-pypy311_pp73-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:614be316e3c06118705fae1f717f9072d35108e5fd4e66a7dd0e80356135340b" },
    { url = "http://***************:3141/zyx/pypi/+f/31f/c0a5bb070722b/levenshtein-0.27.1-pp311-pypy311_pp73-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:31fc0a5bb070722bdabb6f7e14955a294a4a968c68202d294699817f21545d22" },
    { url = "http://***************:3141/zyx/pypi/+f/941/5aa5257227af5/levenshtein-0.27.1-pp311-pypy311_pp73-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:9415aa5257227af543be65768a80c7a75e266c3c818468ce6914812f88f9c3df" },
    { url = "http://***************:3141/zyx/pypi/+f/798/7ef006a3cf56a/levenshtein-0.27.1-pp311-pypy311_pp73-win_amd64.whl", hash = "sha256:7987ef006a3cf56a4532bd4c90c2d3b7b4ca9ad3bf8ae1ee5713c4a3bdfda913" },
]

[[package]]
name = "llvmlite"
version = "0.44.0"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
sdist = { url = "http://***************:3141/zyx/pypi/+f/076/67d66a5d150ab/llvmlite-0.44.0.tar.gz", hash = "sha256:07667d66a5d150abed9157ab6c0b9393c9356f229784a4385c02f99e94fc94d4" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/eed/7d5f29136bda6/llvmlite-0.44.0-cp311-cp311-macosx_10_14_x86_64.whl", hash = "sha256:eed7d5f29136bda63b6d7804c279e2b72e08c952b7c5df61f45db408e0ee52f3" },
    { url = "http://***************:3141/zyx/pypi/+f/ace/564d9fa44bb91/llvmlite-0.44.0-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:ace564d9fa44bb91eb6e6d8e7754977783c68e90a471ea7ce913bff30bd62427" },
    { url = "http://***************:3141/zyx/pypi/+f/c5d/22c3bfc842668/llvmlite-0.44.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:c5d22c3bfc842668168a786af4205ec8e3ad29fb1bc03fd11fd48460d0df64c1" },
    { url = "http://***************:3141/zyx/pypi/+f/f01/a394e9c9b7b1d/llvmlite-0.44.0-cp311-cp311-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:f01a394e9c9b7b1d4e63c327b096d10f6f0ed149ef53d38a09b3749dcf8c9610" },
    { url = "http://***************:3141/zyx/pypi/+f/d84/89634d43c20cd/llvmlite-0.44.0-cp311-cp311-win_amd64.whl", hash = "sha256:d8489634d43c20cd0ad71330dde1d5bc7b9966937a263ff1ec1cebb90dc50955" },
    { url = "http://***************:3141/zyx/pypi/+f/1d6/71a56acf725bf/llvmlite-0.44.0-cp312-cp312-macosx_10_14_x86_64.whl", hash = "sha256:1d671a56acf725bf1b531d5ef76b86660a5ab8ef19bb6a46064a705c6ca80aad" },
    { url = "http://***************:3141/zyx/pypi/+f/5f7/9a728e0435493/llvmlite-0.44.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:5f79a728e0435493611c9f405168682bb75ffd1fbe6fc360733b850c80a026db" },
    { url = "http://***************:3141/zyx/pypi/+f/c01/43a5ef336da14/llvmlite-0.44.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:c0143a5ef336da14deaa8ec26c5449ad5b6a2b564df82fcef4be040b9cacfea9" },
    { url = "http://***************:3141/zyx/pypi/+f/d75/2f89e31b66db6/llvmlite-0.44.0-cp312-cp312-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:d752f89e31b66db6f8da06df8b39f9b91e78c5feea1bf9e8c1fba1d1c24c065d" },
    { url = "http://***************:3141/zyx/pypi/+f/eae/7e2d4ca8f88f8/llvmlite-0.44.0-cp312-cp312-win_amd64.whl", hash = "sha256:eae7e2d4ca8f88f89d315b48c6b741dcb925d6a1042da694aa16ab3dd4cbd3a1" },
    { url = "http://***************:3141/zyx/pypi/+f/319/bddd44e5f71ae/llvmlite-0.44.0-cp313-cp313-macosx_10_14_x86_64.whl", hash = "sha256:319bddd44e5f71ae2689859b7203080716448a3cd1128fb144fe5c055219d516" },
    { url = "http://***************:3141/zyx/pypi/+f/9c5/8867118bad04a/llvmlite-0.44.0-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:9c58867118bad04a0bb22a2e0068c693719658105e40009ffe95c7000fcde88e" },
    { url = "http://***************:3141/zyx/pypi/+f/462/24058b13c96af/llvmlite-0.44.0-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:46224058b13c96af1365290bdfebe9a6264ae62fb79b2b55693deed11657a8bf" },
    { url = "http://***************:3141/zyx/pypi/+f/aa0/097052c32bf72/llvmlite-0.44.0-cp313-cp313-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:aa0097052c32bf721a4efc03bd109d335dfa57d9bffb3d4c24cc680711b8b4fc" },
    { url = "http://***************:3141/zyx/pypi/+f/2fb/7c4f2fb86cbae/llvmlite-0.44.0-cp313-cp313-win_amd64.whl", hash = "sha256:2fb7c4f2fb86cbae6dca3db9ab203eeea0e22d73b99bc2341cdf9de93612e930" },
]

[[package]]
name = "lmdb"
version = "1.7.3"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
sdist = { url = "http://***************:3141/zyx/pypi/+f/d4a/27b7af4fe38f3/lmdb-1.7.3.tar.gz", hash = "sha256:d4a27b7af4fe38f3409d9fbfbf47b617b3d94dee98a00bc8c2a8336ccd3f9b15" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/72e/21d1752b7044f/lmdb-1.7.3-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:72e21d1752b7044fba00c77277e482683db72a62f0a2603c30fce66e3b26306d" },
    { url = "http://***************:3141/zyx/pypi/+f/2be/a10371d95aaae/lmdb-1.7.3-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:2bea10371d95aaae0a48d0b34f76fd6580e73b20190e7834d83def04889b7e55" },
    { url = "http://***************:3141/zyx/pypi/+f/f9d/7b6497be20e07/lmdb-1.7.3-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:f9d7b6497be20e07b17f41a171b1663dfcfe5eac50fe003a256be5231ca5a121" },
    { url = "http://***************:3141/zyx/pypi/+f/f93/9c5222f5725af/lmdb-1.7.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:f939c5222f5725afb5dbdbbd9f2ef20cc1c9bad3696028bf9e5e62ba14e016d2" },
    { url = "http://***************:3141/zyx/pypi/+f/2f4/2cc1d168fbd62/lmdb-1.7.3-cp311-cp311-win_amd64.whl", hash = "sha256:2f42cc1d168fbd625e7981cf45fd8af15a2fb071aaa284e355c7036ad55e2ac7" },
    { url = "http://***************:3141/zyx/pypi/+f/8e1/dc64e139bedae/lmdb-1.7.3-cp311-cp311-win_arm64.whl", hash = "sha256:8e1dc64e139bedaeb2aaa133d1139fdfad53e48a64cd772fb9a1d70fc403c1bc" },
    { url = "http://***************:3141/zyx/pypi/+f/a0c/9d7b35adf0018/lmdb-1.7.3-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:a0c9d7b35adf001884687ca0c256f6c97aedfcc8bb118da92dcdef41e025cb92" },
    { url = "http://***************:3141/zyx/pypi/+f/052/2361cb78bc9e4/lmdb-1.7.3-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:0522361cb78bc9e43003689f2dd3842e4ff8a9a8c11d41b942396f7b94602109" },
    { url = "http://***************:3141/zyx/pypi/+f/92e/e64c2de8185de/lmdb-1.7.3-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:92ee64c2de8185def1eac0d6272626cb57443895b5cd6f35eda848c0426e678e" },
    { url = "http://***************:3141/zyx/pypi/+f/504/38f2b333f5353/lmdb-1.7.3-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:50438f2b333f53539805d3565c0049507e743dff6280229980f1717ef5fcb0ac" },
    { url = "http://***************:3141/zyx/pypi/+f/a2c/ef8c30b6d5138/lmdb-1.7.3-cp312-cp312-win_amd64.whl", hash = "sha256:a2cef8c30b6d5138ef2f7ed1fff661c497f988c5c7e38699295502bfd148a9ce" },
    { url = "http://***************:3141/zyx/pypi/+f/4aa/31d12b16fa0d4/lmdb-1.7.3-cp312-cp312-win_arm64.whl", hash = "sha256:4aa31d12b16fa0d4ab5d49a347a352eb65f8887ae52e9bfe1811b5daff83a064" },
    { url = "http://***************:3141/zyx/pypi/+f/3ef/46b01b09afe81/lmdb-1.7.3-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:3ef46b01b09afe8193449330ea6c0350891d1cdc43cbd20aec8ecdbe40def7d1" },
    { url = "http://***************:3141/zyx/pypi/+f/88d/9b76a11e74588/lmdb-1.7.3-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:88d9b76a11e7458801064df8c837fe23ca28b714205dce05f17a58da3777db6a" },
    { url = "http://***************:3141/zyx/pypi/+f/39d/d69f3dc0910ed/lmdb-1.7.3-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:39dd69f3dc0910ed15ed5db708f195dc3c7f3275d1fb1a8f0a2a4b31db69cdd0" },
    { url = "http://***************:3141/zyx/pypi/+f/ad0/dbec5586bb991/lmdb-1.7.3-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:ad0dbec5586bb991c9ab05f5a26d950e8fa3583e632baf2604512f8059028139" },
    { url = "http://***************:3141/zyx/pypi/+f/d25/279380b28f01f/lmdb-1.7.3-cp313-cp313-win_amd64.whl", hash = "sha256:d25279380b28f01f8f1ef0f783488808bfc2b657c1705c6f383b77c9d04cd8a6" },
    { url = "http://***************:3141/zyx/pypi/+f/1f2/2e79cc7cebc64/lmdb-1.7.3-cp313-cp313-win_arm64.whl", hash = "sha256:1f22e79cc7cebc643c20f1699092bddb6d648d959f4f73becce0befb52690c97" },
    { url = "http://***************:3141/zyx/pypi/+f/4cd/7fb79b2c69dcf/lmdb-1.7.3-py3-none-any.whl", hash = "sha256:4cd7fb79b2c69dcf564d4eec36237e0efc303559176b60ff36fb47ccd3b8b5bb" },
]

[[package]]
name = "matplotlib"
version = "3.10.5"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
dependencies = [
    { name = "contourpy" },
    { name = "cycler" },
    { name = "fonttools" },
    { name = "kiwisolver" },
    { name = "numpy" },
    { name = "packaging" },
    { name = "pillow" },
    { name = "pyparsing" },
    { name = "python-dateutil" },
]
sdist = { url = "http://***************:3141/zyx/pypi/+f/352/ed6ccfb7998a0/matplotlib-3.10.5.tar.gz", hash = "sha256:352ed6ccfb7998a00881692f38b4ca083c691d3e275b4145423704c34c909076" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/dcf/c39c452c6a9f9/matplotlib-3.10.5-cp311-cp311-macosx_10_12_x86_64.whl", hash = "sha256:dcfc39c452c6a9f9028d3e44d2d721484f665304857188124b505b2c95e1eecf" },
    { url = "http://***************:3141/zyx/pypi/+f/903/352681b59f3ef/matplotlib-3.10.5-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:903352681b59f3efbf4546985142a9686ea1d616bb054b09a537a06e4b892ccf" },
    { url = "http://***************:3141/zyx/pypi/+f/080/c3676a56b8ee1/matplotlib-3.10.5-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:080c3676a56b8ee1c762bcf8fca3fe709daa1ee23e6ef06ad9f3fc17332f2d2a" },
    { url = "http://***************:3141/zyx/pypi/+f/4b4/984d5064a35b6/matplotlib-3.10.5-cp311-cp311-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:4b4984d5064a35b6f66d2c11d668565f4389b1119cc64db7a4c1725bc11adffc" },
    { url = "http://***************:3141/zyx/pypi/+f/396/7424121d3a467/matplotlib-3.10.5-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:3967424121d3a46705c9fa9bdb0931de3228f13f73d7bb03c999c88343a89d89" },
    { url = "http://***************:3141/zyx/pypi/+f/337/75bbeb7552855/matplotlib-3.10.5-cp311-cp311-win_amd64.whl", hash = "sha256:33775bbeb75528555a15ac29396940128ef5613cf9a2d31fb1bfd18b3c0c0903" },
    { url = "http://***************:3141/zyx/pypi/+f/c61/333a8e5e6240e/matplotlib-3.10.5-cp311-cp311-win_arm64.whl", hash = "sha256:c61333a8e5e6240e73769d5826b9a31d8b22df76c0778f8480baf1b4b01c9420" },
    { url = "http://***************:3141/zyx/pypi/+f/00b/6feadc28a08bd/matplotlib-3.10.5-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:00b6feadc28a08bd3c65b2894f56cf3c94fc8f7adcbc6ab4516ae1e8ed8f62e2" },
    { url = "http://***************:3141/zyx/pypi/+f/ee9/8a5c5344dc7f4/matplotlib-3.10.5-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:ee98a5c5344dc7f48dc261b6ba5d9900c008fc12beb3fa6ebda81273602cc389" },
    { url = "http://***************:3141/zyx/pypi/+f/a17/e57e33de901d2/matplotlib-3.10.5-cp312-cp312-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:a17e57e33de901d221a07af32c08870ed4528db0b6059dce7d7e65c1122d4bea" },
    { url = "http://***************:3141/zyx/pypi/+f/97b/9d64434190859/matplotlib-3.10.5-cp312-cp312-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:97b9d6443419085950ee4a5b1ee08c363e5c43d7176e55513479e53669e88468" },
    { url = "http://***************:3141/zyx/pypi/+f/cee/fe5d40807d29a/matplotlib-3.10.5-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:ceefe5d40807d29a66ae916c6a3915d60ef9f028ce1927b84e727be91d884369" },
    { url = "http://***************:3141/zyx/pypi/+f/c04/cba0f93d40e45/matplotlib-3.10.5-cp312-cp312-win_amd64.whl", hash = "sha256:c04cba0f93d40e45b3c187c6c52c17f24535b27d545f757a2fffebc06c12b98b" },
    { url = "http://***************:3141/zyx/pypi/+f/a41/bcb6e2c8e79dc/matplotlib-3.10.5-cp312-cp312-win_arm64.whl", hash = "sha256:a41bcb6e2c8e79dc99c5511ae6f7787d2fb52efd3d805fff06d5d4f667db16b2" },
    { url = "http://***************:3141/zyx/pypi/+f/354/204db3f7d5caa/matplotlib-3.10.5-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:354204db3f7d5caaa10e5de74549ef6a05a4550fdd1c8f831ab9bca81efd39ed" },
    { url = "http://***************:3141/zyx/pypi/+f/b07/2aac0c3ad563a/matplotlib-3.10.5-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:b072aac0c3ad563a2b3318124756cb6112157017f7431626600ecbe890df57a1" },
    { url = "http://***************:3141/zyx/pypi/+f/d52/fd5b684d541b5/matplotlib-3.10.5-cp313-cp313-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:d52fd5b684d541b5a51fb276b2b97b010c75bee9aa392f96b4a07aeb491e33c7" },
    { url = "http://***************:3141/zyx/pypi/+f/ee7/a09ae2f467627/matplotlib-3.10.5-cp313-cp313-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:ee7a09ae2f4676276f5a65bd9f2bd91b4f9fbaedf49f40267ce3f9b448de501f" },
    { url = "http://***************:3141/zyx/pypi/+f/ba6/c3c9c067b8348/matplotlib-3.10.5-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:ba6c3c9c067b83481d647af88b4e441d532acdb5ef22178a14935b0b881188f4" },
    { url = "http://***************:3141/zyx/pypi/+f/074/42d2692c9bd1c/matplotlib-3.10.5-cp313-cp313-win_amd64.whl", hash = "sha256:07442d2692c9bd1cceaa4afb4bbe5b57b98a7599de4dabfcca92d3eea70f9ebe" },
    { url = "http://***************:3141/zyx/pypi/+f/48f/e6d47380b68a3/matplotlib-3.10.5-cp313-cp313-win_arm64.whl", hash = "sha256:48fe6d47380b68a37ccfcc94f009530e84d41f71f5dae7eda7c4a5a84aa0a674" },
    { url = "http://***************:3141/zyx/pypi/+f/3b8/0eb8621331449/matplotlib-3.10.5-cp313-cp313t-macosx_10_13_x86_64.whl", hash = "sha256:3b80eb8621331449fc519541a7461987f10afa4f9cfd91afcd2276ebe19bd56c" },
    { url = "http://***************:3141/zyx/pypi/+f/47a/388908e469d6c/matplotlib-3.10.5-cp313-cp313t-macosx_11_0_arm64.whl", hash = "sha256:47a388908e469d6ca2a6015858fa924e0e8a2345a37125948d8e93a91c47933e" },
    { url = "http://***************:3141/zyx/pypi/+f/8b6/b49167d208358/matplotlib-3.10.5-cp313-cp313t-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:8b6b49167d208358983ce26e43aa4196073b4702858670f2eb111f9a10652b4b" },
    { url = "http://***************:3141/zyx/pypi/+f/8a8/da0453a7fd8e3/matplotlib-3.10.5-cp313-cp313t-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:8a8da0453a7fd8e3da114234ba70c5ba9ef0e98f190309ddfde0f089accd46ea" },
    { url = "http://***************:3141/zyx/pypi/+f/52c/6573dfcb7726a/matplotlib-3.10.5-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:52c6573dfcb7726a9907b482cd5b92e6b5499b284ffacb04ffbfe06b3e568124" },
    { url = "http://***************:3141/zyx/pypi/+f/a23/193db2e9d64ec/matplotlib-3.10.5-cp313-cp313t-win_amd64.whl", hash = "sha256:a23193db2e9d64ece69cac0c8231849db7dd77ce59c7b89948cf9d0ce655a3ce" },
    { url = "http://***************:3141/zyx/pypi/+f/56d/a3b102cf6da27/matplotlib-3.10.5-cp313-cp313t-win_arm64.whl", hash = "sha256:56da3b102cf6da2776fef3e71cd96fcf22103a13594a18ac9a9b31314e0be154" },
    { url = "http://***************:3141/zyx/pypi/+f/96e/f8f5a3696f20f/matplotlib-3.10.5-cp314-cp314-macosx_10_13_x86_64.whl", hash = "sha256:96ef8f5a3696f20f55597ffa91c28e2e73088df25c555f8d4754931515512715" },
    { url = "http://***************:3141/zyx/pypi/+f/77f/ab633e94b9da6/matplotlib-3.10.5-cp314-cp314-macosx_11_0_arm64.whl", hash = "sha256:77fab633e94b9da60512d4fa0213daeb76d5a7b05156840c4fd0399b4b818837" },
    { url = "http://***************:3141/zyx/pypi/+f/27f/52634315e96b1/matplotlib-3.10.5-cp314-cp314-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:27f52634315e96b1debbfdc5c416592edcd9c4221bc2f520fd39c33db5d9f202" },
    { url = "http://***************:3141/zyx/pypi/+f/525/f6e28c485c769/matplotlib-3.10.5-cp314-cp314-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:525f6e28c485c769d1f07935b660c864de41c37fd716bfa64158ea646f7084bb" },
    { url = "http://***************:3141/zyx/pypi/+f/1f5/f3ec4c191253c/matplotlib-3.10.5-cp314-cp314-musllinux_1_2_x86_64.whl", hash = "sha256:1f5f3ec4c191253c5f2b7c07096a142c6a1c024d9f738247bfc8e3f9643fc975" },
    { url = "http://***************:3141/zyx/pypi/+f/707/f9c292c4cd471/matplotlib-3.10.5-cp314-cp314-win_amd64.whl", hash = "sha256:707f9c292c4cd4716f19ab8a1f93f26598222cd931e0cd98fbbb1c5994bf7667" },
    { url = "http://***************:3141/zyx/pypi/+f/21a/95b9bf408178d/matplotlib-3.10.5-cp314-cp314-win_arm64.whl", hash = "sha256:21a95b9bf408178d372814de7baacd61c712a62cae560b5e6f35d791776f6516" },
    { url = "http://***************:3141/zyx/pypi/+f/a6b/310f95e1102a8/matplotlib-3.10.5-cp314-cp314t-macosx_10_13_x86_64.whl", hash = "sha256:a6b310f95e1102a8c7c817ef17b60ee5d1851b8c71b63d9286b66b177963039e" },
    { url = "http://***************:3141/zyx/pypi/+f/949/86a242747a060/matplotlib-3.10.5-cp314-cp314t-macosx_11_0_arm64.whl", hash = "sha256:94986a242747a0605cb3ff1cb98691c736f28a59f8ffe5175acaeb7397c49a5a" },
    { url = "http://***************:3141/zyx/pypi/+f/1ff/10ea43288f0c8/matplotlib-3.10.5-cp314-cp314t-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:1ff10ea43288f0c8bab608a305dc6c918cc729d429c31dcbbecde3b9f4d5b569" },
    { url = "http://***************:3141/zyx/pypi/+f/f6a/db644c9d040ff/matplotlib-3.10.5-cp314-cp314t-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:f6adb644c9d040ffb0d3434e440490a66cf73dbfa118a6f79cd7568431f7a012" },
    { url = "http://***************:3141/zyx/pypi/+f/4fa/40a8f98428f78/matplotlib-3.10.5-cp314-cp314t-musllinux_1_2_x86_64.whl", hash = "sha256:4fa40a8f98428f789a9dcacd625f59b7bc4e3ef6c8c7c80187a7a709475cf592" },
    { url = "http://***************:3141/zyx/pypi/+f/956/72a5d628b4420/matplotlib-3.10.5-cp314-cp314t-win_amd64.whl", hash = "sha256:95672a5d628b44207aab91ec20bf59c26da99de12b88f7e0b1fb0a84a86ff959" },
    { url = "http://***************:3141/zyx/pypi/+f/2ef/af97d72629e74/matplotlib-3.10.5-cp314-cp314t-win_arm64.whl", hash = "sha256:2efaf97d72629e74252e0b5e3c46813e9eeaa94e011ecf8084a971a31a97f40b" },
    { url = "http://***************:3141/zyx/pypi/+f/160/e125da27a7494/matplotlib-3.10.5-pp311-pypy311_pp73-macosx_10_15_x86_64.whl", hash = "sha256:160e125da27a749481eaddc0627962990f6029811dbeae23881833a011a0907f" },
    { url = "http://***************:3141/zyx/pypi/+f/ac3/d50760394d78a/matplotlib-3.10.5-pp311-pypy311_pp73-macosx_11_0_arm64.whl", hash = "sha256:ac3d50760394d78a3c9be6b28318fe22b494c4fcf6407e8fd4794b538251899b" },
    { url = "http://***************:3141/zyx/pypi/+f/6c4/9465bf689c4d5/matplotlib-3.10.5-pp311-pypy311_pp73-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:6c49465bf689c4d59d174d0c7795fb42a21d4244d11d70e52b8011987367ac61" },
]

[[package]]
name = "numpy"
version = "1.26.4"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
sdist = { url = "http://***************:3141/zyx/pypi/+f/2a0/2aba9ed12e4ac/numpy-1.26.4.tar.gz", hash = "sha256:2a02aba9ed12e4ac4eb3ea9421c420301a0c6460d9830d74a9df87efa4912010" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/4c6/6707fabe11443/numpy-1.26.4-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:4c66707fabe114439db9068ee468c26bbdf909cac0fb58686a42a24de1760c71" },
    { url = "http://***************:3141/zyx/pypi/+f/edd/8b5fe47dab091/numpy-1.26.4-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:edd8b5fe47dab091176d21bb6de568acdd906d1887a4584a15a9a96a1dca06ef" },
    { url = "http://***************:3141/zyx/pypi/+f/7ab/55401287bfec9/numpy-1.26.4-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:7ab55401287bfec946ced39700c053796e7cc0e3acbef09993a9ad2adba6ca6e" },
    { url = "http://***************:3141/zyx/pypi/+f/666/dbfb6ec68962c/numpy-1.26.4-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:666dbfb6ec68962c033a450943ded891bed2d54e6755e35e5835d63f4f6931d5" },
    { url = "http://***************:3141/zyx/pypi/+f/96f/f0b2ad353d8f9/numpy-1.26.4-cp311-cp311-musllinux_1_1_aarch64.whl", hash = "sha256:96ff0b2ad353d8f990b63294c8986f1ec3cb19d749234014f4e7eb0112ceba5a" },
    { url = "http://***************:3141/zyx/pypi/+f/60d/edbb91afcbfdc/numpy-1.26.4-cp311-cp311-musllinux_1_1_x86_64.whl", hash = "sha256:60dedbb91afcbfdc9bc0b1f3f402804070deed7392c23eb7a7f07fa857868e8a" },
    { url = "http://***************:3141/zyx/pypi/+f/1af/303d6b2210eb8/numpy-1.26.4-cp311-cp311-win32.whl", hash = "sha256:1af303d6b2210eb850fcf03064d364652b7120803a0b872f5211f5234b399f20" },
    { url = "http://***************:3141/zyx/pypi/+f/cd2/5bcecc4974d09/numpy-1.26.4-cp311-cp311-win_amd64.whl", hash = "sha256:cd25bcecc4974d09257ffcd1f098ee778f7834c3ad767fe5db785be9a4aa9cb2" },
    { url = "http://***************:3141/zyx/pypi/+f/b3c/e300f3644fb06/numpy-1.26.4-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:b3ce300f3644fb06443ee2222c2201dd3a89ea6040541412b8fa189341847218" },
    { url = "http://***************:3141/zyx/pypi/+f/03a/8c78d01d9781b/numpy-1.26.4-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:03a8c78d01d9781b28a6989f6fa1bb2c4f2d51201cf99d3dd875df6fbd96b23b" },
    { url = "http://***************:3141/zyx/pypi/+f/9fa/d7dcb1aac3c7f/numpy-1.26.4-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:9fad7dcb1aac3c7f0584a5a8133e3a43eeb2fe127f47e3632d43d677c66c102b" },
    { url = "http://***************:3141/zyx/pypi/+f/675/d61ffbfa78604/numpy-1.26.4-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:675d61ffbfa78604709862923189bad94014bef562cc35cf61d3a07bba02a7ed" },
    { url = "http://***************:3141/zyx/pypi/+f/ab4/7dbe5cc8210f5/numpy-1.26.4-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:ab47dbe5cc8210f55aa58e4805fe224dac469cde56b9f731a4c098b91917159a" },
    { url = "http://***************:3141/zyx/pypi/+f/1dd/a2e7b4ec9dd51/numpy-1.26.4-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:1dda2e7b4ec9dd512f84935c5f126c8bd8b9f2fc001e9f54af255e8c5f16b0e0" },
    { url = "http://***************:3141/zyx/pypi/+f/501/93e430acfc134/numpy-1.26.4-cp312-cp312-win32.whl", hash = "sha256:50193e430acfc1346175fcbdaa28ffec49947a06918b7b92130744e81e640110" },
    { url = "http://***************:3141/zyx/pypi/+f/08b/eddf13648eb95/numpy-1.26.4-cp312-cp312-win_amd64.whl", hash = "sha256:08beddf13648eb95f8d867350f6a018a4be2e5ad54c8d8caed89ebca558b2818" },
]

[[package]]
name = "packaging"
version = "25.0"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
sdist = { url = "http://***************:3141/zyx/pypi/+f/d44/3872c98d677bf/packaging-25.0.tar.gz", hash = "sha256:d443872c98d677bf60f6a1f2f8c1cb748e8fe762d2bf9d3148b5599295b0fc4f" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/295/72ef2b1f17581/packaging-25.0-py3-none-any.whl", hash = "sha256:29572ef2b1f17581046b3a2227d5c611fb25ec70ca1ba8554b24b0e69331a484" },
]

[[package]]
name = "pillow"
version = "11.3.0"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
sdist = { url = "http://***************:3141/zyx/pypi/+f/382/8ee7586cd0b20/pillow-11.3.0.tar.gz", hash = "sha256:3828ee7586cd0b2091b6209e5ad53e20d0649bbe87164a459d0676e035e8f523" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/1cd/110edf8227733/pillow-11.3.0-cp311-cp311-macosx_10_10_x86_64.whl", hash = "sha256:1cd110edf822773368b396281a2293aeb91c90a2db00d78ea43e7e861631b722" },
    { url = "http://***************:3141/zyx/pypi/+f/9c4/12fddd1b77a75/pillow-11.3.0-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:9c412fddd1b77a75aa904615ebaa6001f169b26fd467b4be93aded278266b288" },
    { url = "http://***************:3141/zyx/pypi/+f/7d1/aa4de119a0eca/pillow-11.3.0-cp311-cp311-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:7d1aa4de119a0ecac0a34a9c8bde33f34022e2e8f99104e47a3ca392fd60e37d" },
    { url = "http://***************:3141/zyx/pypi/+f/91d/a1d8822666359/pillow-11.3.0-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:91da1d88226663594e3f6b4b8c3c8d85bd504117d043740a8e0ec449087cc494" },
    { url = "http://***************:3141/zyx/pypi/+f/643/f189248837533/pillow-11.3.0-cp311-cp311-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:643f189248837533073c405ec2f0bb250ba54598cf80e8c1e043381a60632f58" },
    { url = "http://***************:3141/zyx/pypi/+f/106/064daa23a7455/pillow-11.3.0-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:106064daa23a745510dabce1d84f29137a37224831d88eb4ce94bb187b1d7e5f" },
    { url = "http://***************:3141/zyx/pypi/+f/cd8/ff254faf15591/pillow-11.3.0-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:cd8ff254faf15591e724dc7c4ddb6bf4793efcbe13802a4ae3e863cd300b493e" },
    { url = "http://***************:3141/zyx/pypi/+f/932/c754c2d51ad2b/pillow-11.3.0-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:932c754c2d51ad2b2271fd01c3d121daaa35e27efae2a616f77bf164bc0b3e94" },
    { url = "http://***************:3141/zyx/pypi/+f/b4b/8f3efc8d530a1/pillow-11.3.0-cp311-cp311-win32.whl", hash = "sha256:b4b8f3efc8d530a1544e5962bd6b403d5f7fe8b9e08227c6b255f98ad82b4ba0" },
    { url = "http://***************:3141/zyx/pypi/+f/1a9/92e86b0dd7aeb/pillow-11.3.0-cp311-cp311-win_amd64.whl", hash = "sha256:1a992e86b0dd7aeb1f053cd506508c0999d710a8f07b4c791c63843fc6a807ac" },
    { url = "http://***************:3141/zyx/pypi/+f/308/07c931ff7c095/pillow-11.3.0-cp311-cp311-win_arm64.whl", hash = "sha256:30807c931ff7c095620fe04448e2c2fc673fcbb1ffe2a7da3fb39613489b1ddd" },
    { url = "http://***************:3141/zyx/pypi/+f/fda/e223722da47b0/pillow-11.3.0-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:fdae223722da47b024b867c1ea0be64e0df702c5e0a60e27daad39bf960dd1e4" },
    { url = "http://***************:3141/zyx/pypi/+f/921/bd305b10e82b4/pillow-11.3.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:921bd305b10e82b4d1f5e802b6850677f965d8394203d182f078873851dada69" },
    { url = "http://***************:3141/zyx/pypi/+f/eb7/6541cba2f9580/pillow-11.3.0-cp312-cp312-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:eb76541cba2f958032d79d143b98a3a6b3ea87f0959bbe256c0b5e416599fd5d" },
    { url = "http://***************:3141/zyx/pypi/+f/671/72f2944ebba3d/pillow-11.3.0-cp312-cp312-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:67172f2944ebba3d4a7b54f2e95c786a3a50c21b88456329314caaa28cda70f6" },
    { url = "http://***************:3141/zyx/pypi/+f/97f/07ed9f56a3b9b/pillow-11.3.0-cp312-cp312-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:97f07ed9f56a3b9b5f49d3661dc9607484e85c67e27f3e8be2c7d28ca032fec7" },
    { url = "http://***************:3141/zyx/pypi/+f/676/b2815362456b5/pillow-11.3.0-cp312-cp312-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:676b2815362456b5b3216b4fd5bd89d362100dc6f4945154ff172e206a22c024" },
    { url = "http://***************:3141/zyx/pypi/+f/3e1/84b2f26ff1463/pillow-11.3.0-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:3e184b2f26ff146363dd07bde8b711833d7b0202e27d13540bfe2e35a323a809" },
    { url = "http://***************:3141/zyx/pypi/+f/6be/31e3fc9a621e0/pillow-11.3.0-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:6be31e3fc9a621e071bc17bb7de63b85cbe0bfae91bb0363c893cbe67247780d" },
    { url = "http://***************:3141/zyx/pypi/+f/7b1/61756381f0918/pillow-11.3.0-cp312-cp312-win32.whl", hash = "sha256:7b161756381f0918e05e7cb8a371fff367e807770f8fe92ecb20d905d0e1c149" },
    { url = "http://***************:3141/zyx/pypi/+f/a64/44696fce63578/pillow-11.3.0-cp312-cp312-win_amd64.whl", hash = "sha256:a6444696fce635783440b7f7a9fc24b3ad10a9ea3f0ab66c5905be1c19ccf17d" },
    { url = "http://***************:3141/zyx/pypi/+f/2ac/eea54f957dd44/pillow-11.3.0-cp312-cp312-win_arm64.whl", hash = "sha256:2aceea54f957dd4448264f9bf40875da0415c83eb85f55069d89c0ed436e3542" },
    { url = "http://***************:3141/zyx/pypi/+f/1c6/27742b539bba4/pillow-11.3.0-cp313-cp313-ios_13_0_arm64_iphoneos.whl", hash = "sha256:1c627742b539bba4309df89171356fcb3cc5a9178355b2727d1b74a6cf155fbd" },
    { url = "http://***************:3141/zyx/pypi/+f/30b/7c02f3899d10f/pillow-11.3.0-cp313-cp313-ios_13_0_arm64_iphonesimulator.whl", hash = "sha256:30b7c02f3899d10f13d7a48163c8969e4e653f8b43416d23d13d1bbfdc93b9f8" },
    { url = "http://***************:3141/zyx/pypi/+f/785/9a4cc7c9295f5/pillow-11.3.0-cp313-cp313-ios_13_0_x86_64_iphonesimulator.whl", hash = "sha256:7859a4cc7c9295f5838015d8cc0a9c215b77e43d07a25e460f35cf516df8626f" },
    { url = "http://***************:3141/zyx/pypi/+f/ec1/ee50470b0d050/pillow-11.3.0-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:ec1ee50470b0d050984394423d96325b744d55c701a439d2bd66089bff963d3c" },
    { url = "http://***************:3141/zyx/pypi/+f/7db/51d222548ccfd/pillow-11.3.0-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:7db51d222548ccfd274e4572fdbf3e810a5e66b00608862f947b163e613b67dd" },
    { url = "http://***************:3141/zyx/pypi/+f/2d6/fcc902a24ac74/pillow-11.3.0-cp313-cp313-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:2d6fcc902a24ac74495df63faad1884282239265c6839a0a6416d33faedfae7e" },
    { url = "http://***************:3141/zyx/pypi/+f/f0f/5d8f4a08090c6/pillow-11.3.0-cp313-cp313-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:f0f5d8f4a08090c6d6d578351a2b91acf519a54986c055af27e7a93feae6d3f1" },
    { url = "http://***************:3141/zyx/pypi/+f/c37/d8ba9411d6003/pillow-11.3.0-cp313-cp313-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:c37d8ba9411d6003bba9e518db0db0c58a680ab9fe5179f040b0463644bc9805" },
    { url = "http://***************:3141/zyx/pypi/+f/13f/87d581e71d918/pillow-11.3.0-cp313-cp313-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:13f87d581e71d9189ab21fe0efb5a23e9f28552d5be6979e84001d3b8505abe8" },
    { url = "http://***************:3141/zyx/pypi/+f/023/f6d2d11784a46/pillow-11.3.0-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:023f6d2d11784a465f09fd09a34b150ea4672e85fb3d05931d89f373ab14abb2" },
    { url = "http://***************:3141/zyx/pypi/+f/45d/fc51ac5975b93/pillow-11.3.0-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:45dfc51ac5975b938e9809451c51734124e73b04d0f0ac621649821a63852e7b" },
    { url = "http://***************:3141/zyx/pypi/+f/a4d/336baed65d50d/pillow-11.3.0-cp313-cp313-win32.whl", hash = "sha256:a4d336baed65d50d37b88ca5b60c0fa9d81e3a87d4a7930d3880d1624d5b31f3" },
    { url = "http://***************:3141/zyx/pypi/+f/0bc/e5c4fd0921f99/pillow-11.3.0-cp313-cp313-win_amd64.whl", hash = "sha256:0bce5c4fd0921f99d2e858dc4d4d64193407e1b99478bc5cacecba2311abde51" },
    { url = "http://***************:3141/zyx/pypi/+f/190/4e1264881f682/pillow-11.3.0-cp313-cp313-win_arm64.whl", hash = "sha256:1904e1264881f682f02b7f8167935cce37bc97db457f8e7849dc3a6a52b99580" },
    { url = "http://***************:3141/zyx/pypi/+f/4c8/34a3921375c48/pillow-11.3.0-cp313-cp313t-macosx_10_13_x86_64.whl", hash = "sha256:4c834a3921375c48ee6b9624061076bc0a32a60b5532b322cc0ea64e639dd50e" },
    { url = "http://***************:3141/zyx/pypi/+f/5e0/5688ccef30ea6/pillow-11.3.0-cp313-cp313t-macosx_11_0_arm64.whl", hash = "sha256:5e05688ccef30ea69b9317a9ead994b93975104a677a36a8ed8106be9260aa6d" },
    { url = "http://***************:3141/zyx/pypi/+f/101/9b04af07fc016/pillow-11.3.0-cp313-cp313t-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:1019b04af07fc0163e2810167918cb5add8d74674b6267616021ab558dc98ced" },
    { url = "http://***************:3141/zyx/pypi/+f/f94/4255db153ebb2/pillow-11.3.0-cp313-cp313t-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:f944255db153ebb2b19c51fe85dd99ef0ce494123f21b9db4877ffdfc5590c7c" },
    { url = "http://***************:3141/zyx/pypi/+f/1f8/5acb69adf2aae/pillow-11.3.0-cp313-cp313t-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:1f85acb69adf2aaee8b7da124efebbdb959a104db34d3a2cb0f3793dbae422a8" },
    { url = "http://***************:3141/zyx/pypi/+f/05f/6ecbeff500539/pillow-11.3.0-cp313-cp313t-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:05f6ecbeff5005399bb48d198f098a9b4b6bdf27b8487c7f38ca16eeb070cd59" },
    { url = "http://***************:3141/zyx/pypi/+f/a7b/c6e6fd0395bc0/pillow-11.3.0-cp313-cp313t-musllinux_1_2_aarch64.whl", hash = "sha256:a7bc6e6fd0395bc052f16b1a8670859964dbd7003bd0af2ff08342eb6e442cfe" },
    { url = "http://***************:3141/zyx/pypi/+f/83e/1b0161c9d1481/pillow-11.3.0-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:83e1b0161c9d148125083a35c1c5a89db5b7054834fd4387499e06552035236c" },
    { url = "http://***************:3141/zyx/pypi/+f/2a3/117c06b8fb646/pillow-11.3.0-cp313-cp313t-win32.whl", hash = "sha256:2a3117c06b8fb646639dce83694f2f9eac405472713fcb1ae887469c0d4f6788" },
    { url = "http://***************:3141/zyx/pypi/+f/857/844335c95bea9/pillow-11.3.0-cp313-cp313t-win_amd64.whl", hash = "sha256:857844335c95bea93fb39e0fa2726b4d9d758850b34075a7e3ff4f4fa3aa3b31" },
    { url = "http://***************:3141/zyx/pypi/+f/879/7edc41f3e8536/pillow-11.3.0-cp313-cp313t-win_arm64.whl", hash = "sha256:8797edc41f3e8536ae4b10897ee2f637235c94f27404cac7297f7b607dd0716e" },
    { url = "http://***************:3141/zyx/pypi/+f/d9d/a3df5f9ea2a89/pillow-11.3.0-cp314-cp314-macosx_10_13_x86_64.whl", hash = "sha256:d9da3df5f9ea2a89b81bb6087177fb1f4d1c7146d583a3fe5c672c0d94e55e12" },
    { url = "http://***************:3141/zyx/pypi/+f/0b2/75ff9b04df7b6/pillow-11.3.0-cp314-cp314-macosx_11_0_arm64.whl", hash = "sha256:0b275ff9b04df7b640c59ec5a3cb113eefd3795a8df80bac69646ef699c6981a" },
    { url = "http://***************:3141/zyx/pypi/+f/074/3841cabd3dba6/pillow-11.3.0-cp314-cp314-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:0743841cabd3dba6a83f38a92672cccbd69af56e3e91777b0ee7f4dba4385632" },
    { url = "http://***************:3141/zyx/pypi/+f/246/5a69cf967b8b4/pillow-11.3.0-cp314-cp314-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:2465a69cf967b8b49ee1b96d76718cd98c4e925414ead59fdf75cf0fd07df673" },
    { url = "http://***************:3141/zyx/pypi/+f/417/4263813942470/pillow-11.3.0-cp314-cp314-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:41742638139424703b4d01665b807c6468e23e699e8e90cffefe291c5832b027" },
    { url = "http://***************:3141/zyx/pypi/+f/93e/fb0b4de7e340d/pillow-11.3.0-cp314-cp314-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:93efb0b4de7e340d99057415c749175e24c8864302369e05914682ba642e5d77" },
    { url = "http://***************:3141/zyx/pypi/+f/796/6e38dcd0fa11c/pillow-11.3.0-cp314-cp314-musllinux_1_2_aarch64.whl", hash = "sha256:7966e38dcd0fa11ca390aed7c6f20454443581d758242023cf36fcb319b1a874" },
    { url = "http://***************:3141/zyx/pypi/+f/98a/9afa7b9007c67/pillow-11.3.0-cp314-cp314-musllinux_1_2_x86_64.whl", hash = "sha256:98a9afa7b9007c67ed84c57c9e0ad86a6000da96eaa638e4f8abe5b65ff83f0a" },
    { url = "http://***************:3141/zyx/pypi/+f/02a/723e6bf909e7c/pillow-11.3.0-cp314-cp314-win32.whl", hash = "sha256:02a723e6bf909e7cea0dac1b0e0310be9d7650cd66222a5f1c571455c0a45214" },
    { url = "http://***************:3141/zyx/pypi/+f/a41/8486160228f64/pillow-11.3.0-cp314-cp314-win_amd64.whl", hash = "sha256:a418486160228f64dd9e9efcd132679b7a02a5f22c982c78b6fc7dab3fefb635" },
    { url = "http://***************:3141/zyx/pypi/+f/155/658efb5e04466/pillow-11.3.0-cp314-cp314-win_arm64.whl", hash = "sha256:155658efb5e044669c08896c0c44231c5e9abcaadbc5cd3648df2f7c0b96b9a6" },
    { url = "http://***************:3141/zyx/pypi/+f/59a/03cdf019efbfe/pillow-11.3.0-cp314-cp314t-macosx_10_13_x86_64.whl", hash = "sha256:59a03cdf019efbfeeed910bf79c7c93255c3d54bc45898ac2a4140071b02b4ae" },
    { url = "http://***************:3141/zyx/pypi/+f/f8a/5827f84d973d8/pillow-11.3.0-cp314-cp314t-macosx_11_0_arm64.whl", hash = "sha256:f8a5827f84d973d8636e9dc5764af4f0cf2318d26744b3d902931701b0d46653" },
    { url = "http://***************:3141/zyx/pypi/+f/ee9/2f2fd10f4adc4/pillow-11.3.0-cp314-cp314t-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:ee92f2fd10f4adc4b43d07ec5e779932b4eb3dbfbc34790ada5a6669bc095aa6" },
    { url = "http://***************:3141/zyx/pypi/+f/c96/d333dcf42d01f/pillow-11.3.0-cp314-cp314t-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:c96d333dcf42d01f47b37e0979b6bd73ec91eae18614864622d9b87bbd5bbf36" },
    { url = "http://***************:3141/zyx/pypi/+f/4c9/6f993ab8c9846/pillow-11.3.0-cp314-cp314t-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:4c96f993ab8c98460cd0c001447bff6194403e8b1d7e149ade5f00594918128b" },
    { url = "http://***************:3141/zyx/pypi/+f/413/42b64afeba938/pillow-11.3.0-cp314-cp314t-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:41342b64afeba938edb034d122b2dda5db2139b9a4af999729ba8818e0056477" },
    { url = "http://***************:3141/zyx/pypi/+f/068/d9c39a2d1b358/pillow-11.3.0-cp314-cp314t-musllinux_1_2_aarch64.whl", hash = "sha256:068d9c39a2d1b358eb9f245ce7ab1b5c3246c7c8c7d9ba58cfa5b43146c06e50" },
    { url = "http://***************:3141/zyx/pypi/+f/a1b/c6ba083b14518/pillow-11.3.0-cp314-cp314t-musllinux_1_2_x86_64.whl", hash = "sha256:a1bc6ba083b145187f648b667e05a2534ecc4b9f2784c2cbe3089e44868f2b9b" },
    { url = "http://***************:3141/zyx/pypi/+f/118/ca10c0d60b06d/pillow-11.3.0-cp314-cp314t-win32.whl", hash = "sha256:118ca10c0d60b06d006be10a501fd6bbdfef559251ed31b794668ed569c87e12" },
    { url = "http://***************:3141/zyx/pypi/+f/892/4748b688aa210/pillow-11.3.0-cp314-cp314t-win_amd64.whl", hash = "sha256:8924748b688aa210d79883357d102cd64690e56b923a186f35a82cbc10f997db" },
    { url = "http://***************:3141/zyx/pypi/+f/79e/a0d14d3ebad43/pillow-11.3.0-cp314-cp314t-win_arm64.whl", hash = "sha256:79ea0d14d3ebad43ec77ad5272e6ff9bba5b679ef73375ea760261207fa8e0aa" },
    { url = "http://***************:3141/zyx/pypi/+f/7c8/ec7a017ad1bd5/pillow-11.3.0-pp311-pypy311_pp73-macosx_10_15_x86_64.whl", hash = "sha256:7c8ec7a017ad1bd562f93dbd8505763e688d388cde6e4a010ae1486916e713e6" },
    { url = "http://***************:3141/zyx/pypi/+f/9ab/6ae226de48019/pillow-11.3.0-pp311-pypy311_pp73-macosx_11_0_arm64.whl", hash = "sha256:9ab6ae226de48019caa8074894544af5b53a117ccb9d3b3dcb2871464c829438" },
    { url = "http://***************:3141/zyx/pypi/+f/fe2/7fb049cdcca11/pillow-11.3.0-pp311-pypy311_pp73-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:fe27fb049cdcca11f11a7bfda64043c37b30e6b91f10cb5bab275806c32f6ab3" },
    { url = "http://***************:3141/zyx/pypi/+f/465/b9e8844e3c351/pillow-11.3.0-pp311-pypy311_pp73-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:465b9e8844e3c3519a983d58b80be3f668e2a7a5db97f2784e7079fbc9f9822c" },
    { url = "http://***************:3141/zyx/pypi/+f/541/8b53c0d59b382/pillow-11.3.0-pp311-pypy311_pp73-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:5418b53c0d59b3824d05e029669efa023bbef0f3e92e75ec8428f3799487f361" },
    { url = "http://***************:3141/zyx/pypi/+f/504/b6f59505f08ae/pillow-11.3.0-pp311-pypy311_pp73-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:504b6f59505f08ae014f724b6207ff6222662aab5cc9542577fb084ed0676ac7" },
    { url = "http://***************:3141/zyx/pypi/+f/c84/d689db21a1c39/pillow-11.3.0-pp311-pypy311_pp73-win_amd64.whl", hash = "sha256:c84d689db21a1c397d001aa08241044aa2069e7587b398c8cc63020390b1c1b8" },
]

[[package]]
name = "prettytable"
version = "3.16.0"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
dependencies = [
    { name = "wcwidth" },
]
sdist = { url = "http://***************:3141/zyx/pypi/+f/3c6/4b31719d961bf/prettytable-3.16.0.tar.gz", hash = "sha256:3c64b31719d961bf69c9a7e03d0c1e477320906a98da63952bc6698d6164ff57" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/b5e/ccfabb82222f5/prettytable-3.16.0-py3-none-any.whl", hash = "sha256:b5eccfabb82222f5aa46b798ff02a8452cf530a352c31bddfa29be41242863aa" },
]

[[package]]
name = "pycparser"
version = "2.22"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
sdist = { url = "http://***************:3141/zyx/pypi/+f/491/c8be9c040f539/pycparser-2.22.tar.gz", hash = "sha256:491c8be9c040f5390f5bf44a5b07752bd07f56edf992381b05c701439eec10f6" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/c37/02b6d3dd8c7ab/pycparser-2.22-py3-none-any.whl", hash = "sha256:c3702b6d3dd8c7abc1afa565d7e63d53a1d0bd86cdc24edd75470f4de499cfcc" },
]

[[package]]
name = "pygit2"
version = "1.18.1"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
dependencies = [
    { name = "cffi" },
]
sdist = { url = "http://***************:3141/zyx/pypi/+f/84e/06fc3708b8d3b/pygit2-1.18.1.tar.gz", hash = "sha256:84e06fc3708b8d3beeefcec637f61d87deb38272e7487ea1c529174184fff6c4" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/1af/c440f3932daff/pygit2-1.18.1-cp311-cp311-macosx_10_9_universal2.whl", hash = "sha256:1afc440f3932daffd51a7f405dfe39fc306f6dab343cb7b6f656699e76392d3e" },
    { url = "http://***************:3141/zyx/pypi/+f/c13/b314c4bce72ef/pygit2-1.18.1-cp311-cp311-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:c13b314c4bce72ef094730c7eb887847a74c2099e8303fb124ec6fd537dba51d" },
    { url = "http://***************:3141/zyx/pypi/+f/a2f/0a6fe03206fd4/pygit2-1.18.1-cp311-cp311-manylinux_2_27_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:a2f0a6fe03206fd4b359d3dabc4d6355e383873bc732387af92c59bfc47a9626" },
    { url = "http://***************:3141/zyx/pypi/+f/7b2/156b457942e82/pygit2-1.18.1-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:7b2156b457942e82c5bee69e6786ec97e88bd63a06c5717139d37175806fcd4f" },
    { url = "http://***************:3141/zyx/pypi/+f/1bc/04d7bc894926e/pygit2-1.18.1-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:1bc04d7bc894926e78062115740b7f6d203b60e9a4e9c2a83ed59ff2ae588b58" },
    { url = "http://***************:3141/zyx/pypi/+f/6f6/46452d4426cd7/pygit2-1.18.1-cp311-cp311-win32.whl", hash = "sha256:6f646452d4426cd7c269c5e30985a04c4955f78c214796af7bc35979c477e916" },
    { url = "http://***************:3141/zyx/pypi/+f/709/b25ce78cb2e07/pygit2-1.18.1-cp311-cp311-win_amd64.whl", hash = "sha256:709b25ce78cb2e07ee4cf1122d2c613788d95c949de887e716e23799cfc435b1" },
    { url = "http://***************:3141/zyx/pypi/+f/d4d/756c2a1bb5da1/pygit2-1.18.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:d4d756c2a1bb5da18f83c319fa63dda8099ad091999e181771616cb275a73bc9" },
    { url = "http://***************:3141/zyx/pypi/+f/949/ce475c1c92056/pygit2-1.18.1-cp312-cp312-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:949ce475c1c92056c3811352fd54957eecb0349ae50b6b81d32027aa21882c8d" },
    { url = "http://***************:3141/zyx/pypi/+f/d11/005e9a42ed791/pygit2-1.18.1-cp312-cp312-manylinux_2_27_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:d11005e9a42ed7914f569dca76a74e745087b8b8194597746337e1a68886320e" },
    { url = "http://***************:3141/zyx/pypi/+f/942/0176d4a0ebab5/pygit2-1.18.1-cp312-cp312-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:9420176d4a0ebab5c9e3731e5cdeb9a020211393c6b311aa78a7e3254ceff965" },
    { url = "http://***************:3141/zyx/pypi/+f/a57/a0eda85ec5f74/pygit2-1.18.1-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:a57a0eda85ec5f74a1e59019ccf567e4ec509e6d20dc54c3f91d7a0b7d570298" },
    { url = "http://***************:3141/zyx/pypi/+f/ee4/7091a1a0242a8/pygit2-1.18.1-cp312-cp312-win32.whl", hash = "sha256:ee47091a1a0242a8b4f05c430f7d81945eec4cdef855b23b31446c53f42a3ed5" },
    { url = "http://***************:3141/zyx/pypi/+f/788/96bc55030855d/pygit2-1.18.1-cp312-cp312-win_amd64.whl", hash = "sha256:78896bc55030855dd9ee0e92157fb2417b22e254a2194f94b9d249e954fb1d53" },
    { url = "http://***************:3141/zyx/pypi/+f/e49/c3dfd04fd33aa/pygit2-1.18.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:e49c3dfd04fd33aaea741ccaf9522ab787e0b6dfbe1f6250c549802152a27d39" },
    { url = "http://***************:3141/zyx/pypi/+f/b2e/71c4bf35f3a21/pygit2-1.18.1-cp313-cp313-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:b2e71c4bf35f3a2106aa3f6c16baca1c0ed0972d8e66e9ec16e52a29e9c630f2" },
    { url = "http://***************:3141/zyx/pypi/+f/e6c/d164c44eaa04b/pygit2-1.18.1-cp313-cp313-manylinux_2_27_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:e6cd164c44eaa04bcb0b4c1e14eeefa4035df99a000d8cf30982221d9b852c75" },
    { url = "http://***************:3141/zyx/pypi/+f/a61/c13de91b0eef6/pygit2-1.18.1-cp313-cp313-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:a61c13de91b0eef6fd5a4d97b64f54bebd411a57776cad9376fe14d811144811" },
    { url = "http://***************:3141/zyx/pypi/+f/3e1/58de94222dce9/pygit2-1.18.1-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:3e158de94222dce906d06f159acac273d79ee76af975cb4921b113113f4eac98" },
    { url = "http://***************:3141/zyx/pypi/+f/99e/b8a7aa40142d0/pygit2-1.18.1-cp313-cp313-win32.whl", hash = "sha256:99eb8a7aa40142d0779779fdb53ca7439b5913c114df4e2550c5a7cfc2123181" },
    { url = "http://***************:3141/zyx/pypi/+f/1fd/ec95cb13f95ba/pygit2-1.18.1-cp313-cp313-win_amd64.whl", hash = "sha256:1fdec95cb13f95ba0c83b24f33861e3a8eca502acfadadd2f8c1dc26450d79c4" },
    { url = "http://***************:3141/zyx/pypi/+f/fcf/759e92ced4fb1/pygit2-1.18.1-pp311-pypy311_pp73-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:fcf759e92ced4fb148f5247f1fb1ee634c750024341ef320511d87216fbb0e7f" },
    { url = "http://***************:3141/zyx/pypi/+f/321/dcb4198d7fc02/pygit2-1.18.1-pp311-pypy311_pp73-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:321dcb4198d7fc02949ae0942a626ebf779ede8ed8bb2cbf80a012a32d840aca" },
]

[[package]]
name = "pyparsing"
version = "3.2.3"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
sdist = { url = "http://***************:3141/zyx/pypi/+f/b9c/13f1ab8b3b542/pyparsing-3.2.3.tar.gz", hash = "sha256:b9c13f1ab8b3b542f72e28f634bad4de758ab3ce4546e4301970ad6fa77c38be" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/a74/9938e02d6fd0b/pyparsing-3.2.3-py3-none-any.whl", hash = "sha256:a749938e02d6fd0b59b356ca504a24982314bb090c383e3cf201c95ef7e2bfcf" },
]

[[package]]
name = "pyqtgraph"
version = "0.13.7"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
dependencies = [
    { name = "numpy" },
]
sdist = { url = "http://***************:3141/zyx/pypi/+f/64f/84f1935c6996d/pyqtgraph-0.13.7.tar.gz", hash = "sha256:64f84f1935c6996d0e09b1ee66fe478a7771e3ca6f3aaa05f00f6e068321d9e3" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/775/4edbefb6c367f/pyqtgraph-0.13.7-py3-none-any.whl", hash = "sha256:7754edbefb6c367fa0dfb176e2d0610da3ada20aa7a5318516c74af5fb72bf7a" },
]

[[package]]
name = "python-dateutil"
version = "2.9.0.post0"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
dependencies = [
    { name = "six" },
]
sdist = { url = "http://***************:3141/zyx/pypi/+f/37d/d54208da7e1cd/python-dateutil-2.9.0.post0.tar.gz", hash = "sha256:37dd54208da7e1cd875388217d5e00ebd4179249f90fb72437e91a35459a0ad3" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/a8b/2bc7bffae2822/python_dateutil-2.9.0.post0-py2.py3-none-any.whl", hash = "sha256:a8b2bc7bffae282281c8140a97d3aa9c14da0b136dfe83f850eea9a5f7470427" },
]

[[package]]
name = "pythonparser"
version = "1.4"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
dependencies = [
    { name = "regex" },
]
sdist = { url = "http://***************:3141/zyx/devpi/+f/2d5/d50e853f4fd24/pythonparser-1.4.tar.gz", hash = "sha256:2d5d50e853f4fd24f8b999e35df9001b99462311512f80592861b156b3ffbc40" }
wheels = [
    { url = "http://***************:3141/zyx/devpi/+f/bcb/3ec56f08e78f6/pythonparser-1.4-py3-none-any.whl", hash = "sha256:bcb3ec56f08e78f61b7bace1a9da45deab4ae459240b8c520be3347e68e67aad" },
]

[[package]]
name = "qasync"
version = "0.27.1"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
sdist = { url = "http://***************:3141/zyx/pypi/+f/8dc/768fd1ee5de10/qasync-0.27.1.tar.gz", hash = "sha256:8dc768fd1ee5de1044c7c305eccf2d39d24d87803ea71189d4024fb475f4985f" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/5d5/7335723bc7d9b/qasync-0.27.1-py3-none-any.whl", hash = "sha256:5d57335723bc7d9b328dadd8cb2ed7978640e4bf2da184889ce50ee3ad2602c7" },
]

[[package]]
name = "rapidfuzz"
version = "3.13.0"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
sdist = { url = "http://***************:3141/zyx/pypi/+f/d2e/af3839e52cbcc/rapidfuzz-3.13.0.tar.gz", hash = "sha256:d2eaf3839e52cbcc0accbe9817a67b4b0fcf70aaeb229cfddc1c28061f9ce5d8" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/d39/5a5cad0c09c7f/rapidfuzz-3.13.0-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:d395a5cad0c09c7f096433e5fd4224d83b53298d53499945a9b0e5a971a84f3a" },
    { url = "http://***************:3141/zyx/pypi/+f/b7b/3eda607a01916/rapidfuzz-3.13.0-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:b7b3eda607a019169f7187328a8d1648fb9a90265087f6903d7ee3a8eee01805" },
    { url = "http://***************:3141/zyx/pypi/+f/98e/0bfa602e1942d/rapidfuzz-3.13.0-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:98e0bfa602e1942d542de077baf15d658bd9d5dcfe9b762aff791724c1c38b70" },
    { url = "http://***************:3141/zyx/pypi/+f/bef/86df6d59667d9/rapidfuzz-3.13.0-cp311-cp311-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:bef86df6d59667d9655905b02770a0c776d2853971c0773767d5ef8077acd624" },
    { url = "http://***************:3141/zyx/pypi/+f/fed/d316c165beed6/rapidfuzz-3.13.0-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:fedd316c165beed6307bf754dee54d3faca2c47e1f3bcbd67595001dfa11e969" },
    { url = "http://***************:3141/zyx/pypi/+f/515/8da7f2ec02a93/rapidfuzz-3.13.0-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:5158da7f2ec02a930be13bac53bb5903527c073c90ee37804090614cab83c29e" },
    { url = "http://***************:3141/zyx/pypi/+f/3b6/f913ee4618ddb/rapidfuzz-3.13.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:3b6f913ee4618ddb6d6f3e387b76e8ec2fc5efee313a128809fbd44e65c2bbb2" },
    { url = "http://***************:3141/zyx/pypi/+f/d25/fdbce6459ccbb/rapidfuzz-3.13.0-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:d25fdbce6459ccbbbf23b4b044f56fbd1158b97ac50994eaae2a1c0baae78301" },
    { url = "http://***************:3141/zyx/pypi/+f/253/43ccc589a4579/rapidfuzz-3.13.0-cp311-cp311-musllinux_1_2_i686.whl", hash = "sha256:25343ccc589a4579fbde832e6a1e27258bfdd7f2eb0f28cb836d6694ab8591fc" },
    { url = "http://***************:3141/zyx/pypi/+f/a9a/d1f37894e3ffb/rapidfuzz-3.13.0-cp311-cp311-musllinux_1_2_ppc64le.whl", hash = "sha256:a9ad1f37894e3ffb76bbab76256e8a8b789657183870be11aa64e306bb5228fd" },
    { url = "http://***************:3141/zyx/pypi/+f/5dc/71ef23845bb6b/rapidfuzz-3.13.0-cp311-cp311-musllinux_1_2_s390x.whl", hash = "sha256:5dc71ef23845bb6b62d194c39a97bb30ff171389c9812d83030c1199f319098c" },
    { url = "http://***************:3141/zyx/pypi/+f/b7f/4c65facdb94f4/rapidfuzz-3.13.0-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:b7f4c65facdb94f44be759bbd9b6dda1fa54d0d6169cdf1a209a5ab97d311a75" },
    { url = "http://***************:3141/zyx/pypi/+f/b51/04b62711565e0/rapidfuzz-3.13.0-cp311-cp311-win32.whl", hash = "sha256:b5104b62711565e0ff6deab2a8f5dbf1fbe333c5155abe26d2cfd6f1849b6c87" },
    { url = "http://***************:3141/zyx/pypi/+f/909/3cdeb926deb32/rapidfuzz-3.13.0-cp311-cp311-win_amd64.whl", hash = "sha256:9093cdeb926deb32a4887ebe6910f57fbcdbc9fbfa52252c10b56ef2efb0289f" },
    { url = "http://***************:3141/zyx/pypi/+f/f70/f646751b6aa9d/rapidfuzz-3.13.0-cp311-cp311-win_arm64.whl", hash = "sha256:f70f646751b6aa9d05be1fb40372f006cc89d6aad54e9d79ae97bd1f5fce5203" },
    { url = "http://***************:3141/zyx/pypi/+f/4a1/a6a906ba62f25/rapidfuzz-3.13.0-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:4a1a6a906ba62f2556372282b1ef37b26bca67e3d2ea957277cfcefc6275cca7" },
    { url = "http://***************:3141/zyx/pypi/+f/2fd/0975e015b05c7/rapidfuzz-3.13.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:2fd0975e015b05c79a97f38883a11236f5a24cca83aa992bd2558ceaa5652b26" },
    { url = "http://***************:3141/zyx/pypi/+f/5d4/e13593d298c50/rapidfuzz-3.13.0-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:5d4e13593d298c50c4f94ce453f757b4b398af3fa0fd2fde693c3e51195b7f69" },
    { url = "http://***************:3141/zyx/pypi/+f/ed6/f416bda1c9133/rapidfuzz-3.13.0-cp312-cp312-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:ed6f416bda1c9133000009d84d9409823eb2358df0950231cc936e4bf784eb97" },
    { url = "http://***************:3141/zyx/pypi/+f/1dc/82b6ed01acb53/rapidfuzz-3.13.0-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:1dc82b6ed01acb536b94a43996a94471a218f4d89f3fdd9185ab496de4b2a981" },
    { url = "http://***************:3141/zyx/pypi/+f/e9d/824de871daa6e/rapidfuzz-3.13.0-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:e9d824de871daa6e443b39ff495a884931970d567eb0dfa213d234337343835f" },
    { url = "http://***************:3141/zyx/pypi/+f/2d1/8228a2390375c/rapidfuzz-3.13.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:2d18228a2390375cf45726ce1af9d36ff3dc1f11dce9775eae1f1b13ac6ec50f" },
    { url = "http://***************:3141/zyx/pypi/+f/9f5/fe634c9482ec5/rapidfuzz-3.13.0-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:9f5fe634c9482ec5d4a6692afb8c45d370ae86755e5f57aa6c50bfe4ca2bdd87" },
    { url = "http://***************:3141/zyx/pypi/+f/694/eb531889f7102/rapidfuzz-3.13.0-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:694eb531889f71022b2be86f625a4209c4049e74be9ca836919b9e395d5e33b3" },
    { url = "http://***************:3141/zyx/pypi/+f/11b/47b40650e0614/rapidfuzz-3.13.0-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:11b47b40650e06147dee5e51a9c9ad73bb7b86968b6f7d30e503b9f8dd1292db" },
    { url = "http://***************:3141/zyx/pypi/+f/98b/8107ff14f5af0/rapidfuzz-3.13.0-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:98b8107ff14f5af0243f27d236bcc6e1ef8e7e3b3c25df114e91e3a99572da73" },
    { url = "http://***************:3141/zyx/pypi/+f/b83/6f486dba0aceb/rapidfuzz-3.13.0-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:b836f486dba0aceb2551e838ff3f514a38ee72b015364f739e526d720fdb823a" },
    { url = "http://***************:3141/zyx/pypi/+f/467/1ee300d1818d7/rapidfuzz-3.13.0-cp312-cp312-win32.whl", hash = "sha256:4671ee300d1818d7bdfd8fa0608580d7778ba701817216f0c17fb29e6b972514" },
    { url = "http://***************:3141/zyx/pypi/+f/6e2/065f68fb1d0bf/rapidfuzz-3.13.0-cp312-cp312-win_amd64.whl", hash = "sha256:6e2065f68fb1d0bf65adc289c1bdc45ba7e464e406b319d67bb54441a1b9da9e" },
    { url = "http://***************:3141/zyx/pypi/+f/65c/c97c2fc2c2fe2/rapidfuzz-3.13.0-cp312-cp312-win_arm64.whl", hash = "sha256:65cc97c2fc2c2fe23586599686f3b1ceeedeca8e598cfcc1b7e56dc8ca7e2aa7" },
    { url = "http://***************:3141/zyx/pypi/+f/09e/908064d3684c5/rapidfuzz-3.13.0-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:09e908064d3684c541d312bd4c7b05acb99a2c764f6231bd507d4b4b65226c23" },
    { url = "http://***************:3141/zyx/pypi/+f/57c/390336cb50d5d/rapidfuzz-3.13.0-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:57c390336cb50d5d3bfb0cfe1467478a15733703af61f6dffb14b1cd312a6fae" },
    { url = "http://***************:3141/zyx/pypi/+f/0da/54aa8547b3c2c/rapidfuzz-3.13.0-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:0da54aa8547b3c2c188db3d1c7eb4d1bb6dd80baa8cdaeaec3d1da3346ec9caa" },
    { url = "http://***************:3141/zyx/pypi/+f/df8/e8c21e67afb9d/rapidfuzz-3.13.0-cp313-cp313-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:df8e8c21e67afb9d7fbe18f42c6111fe155e801ab103c81109a61312927cc611" },
    { url = "http://***************:3141/zyx/pypi/+f/461/fd13250a2adf8/rapidfuzz-3.13.0-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:461fd13250a2adf8e90ca9a0e1e166515cbcaa5e9c3b1f37545cbbeff9e77f6b" },
    { url = "http://***************:3141/zyx/pypi/+f/c2b/3dd5d206a12de/rapidfuzz-3.13.0-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:c2b3dd5d206a12deca16870acc0d6e5036abeb70e3cad6549c294eff15591527" },
    { url = "http://***************:3141/zyx/pypi/+f/134/3d745fbf4688e/rapidfuzz-3.13.0-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:1343d745fbf4688e412d8f398c6e6d6f269db99a54456873f232ba2e7aeb4939" },
    { url = "http://***************:3141/zyx/pypi/+f/b1b/065f370d54551/rapidfuzz-3.13.0-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:b1b065f370d54551dcc785c6f9eeb5bd517ae14c983d2784c064b3aa525896df" },
    { url = "http://***************:3141/zyx/pypi/+f/11b/125d8edd67e76/rapidfuzz-3.13.0-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:11b125d8edd67e767b2295eac6eb9afe0b1cdc82ea3d4b9257da4b8e06077798" },
    { url = "http://***************:3141/zyx/pypi/+f/c33/f9c841630b2bb/rapidfuzz-3.13.0-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:c33f9c841630b2bb7e69a3fb5c84a854075bb812c47620978bddc591f764da3d" },
    { url = "http://***************:3141/zyx/pypi/+f/ae4/574cb66cf1e85/rapidfuzz-3.13.0-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:ae4574cb66cf1e85d32bb7e9ec45af5409c5b3970b7ceb8dea90168024127566" },
    { url = "http://***************:3141/zyx/pypi/+f/e05/752418b24bbd4/rapidfuzz-3.13.0-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:e05752418b24bbd411841b256344c26f57da1148c5509e34ea39c7eb5099ab72" },
    { url = "http://***************:3141/zyx/pypi/+f/0e1/d08cb884805a5/rapidfuzz-3.13.0-cp313-cp313-win32.whl", hash = "sha256:0e1d08cb884805a543f2de1f6744069495ef527e279e05370dd7c83416af83f8" },
    { url = "http://***************:3141/zyx/pypi/+f/9a7/c6232be5f809c/rapidfuzz-3.13.0-cp313-cp313-win_amd64.whl", hash = "sha256:9a7c6232be5f809cd39da30ee5d24e6cadd919831e6020ec6c2391f4c3bc9264" },
    { url = "http://***************:3141/zyx/pypi/+f/3f3/2f15bacd1838c/rapidfuzz-3.13.0-cp313-cp313-win_arm64.whl", hash = "sha256:3f32f15bacd1838c929b35c84b43618481e1b3d7a61b5ed2db0291b70ae88b53" },
    { url = "http://***************:3141/zyx/pypi/+f/1ba/007f4d35a45ee/rapidfuzz-3.13.0-pp311-pypy311_pp73-macosx_10_15_x86_64.whl", hash = "sha256:1ba007f4d35a45ee68656b2eb83b8715e11d0f90e5b9f02d615a8a321ff00c27" },
    { url = "http://***************:3141/zyx/pypi/+f/d7a/217310429b43b/rapidfuzz-3.13.0-pp311-pypy311_pp73-macosx_11_0_arm64.whl", hash = "sha256:d7a217310429b43be95b3b8ad7f8fc41aba341109dc91e978cd7c703f928c58f" },
    { url = "http://***************:3141/zyx/pypi/+f/558/bf526bcd777de/rapidfuzz-3.13.0-pp311-pypy311_pp73-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:558bf526bcd777de32b7885790a95a9548ffdcce68f704a81207be4a286c1095" },
    { url = "http://***************:3141/zyx/pypi/+f/202/a87760f514514/rapidfuzz-3.13.0-pp311-pypy311_pp73-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:202a87760f5145140d56153b193a797ae9338f7939eb16652dd7ff96f8faf64c" },
    { url = "http://***************:3141/zyx/pypi/+f/cfc/ccc08f671646c/rapidfuzz-3.13.0-pp311-pypy311_pp73-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:cfcccc08f671646ccb1e413c773bb92e7bba789e3a1796fd49d23c12539fe2e4" },
    { url = "http://***************:3141/zyx/pypi/+f/1f2/19f1e3c3194d7/rapidfuzz-3.13.0-pp311-pypy311_pp73-win_amd64.whl", hash = "sha256:1f219f1e3c3194d7a7de222f54450ce12bc907862ff9a8962d83061c1f923c86" },
]

[[package]]
name = "regex"
version = "2024.11.6"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
sdist = { url = "http://***************:3141/zyx/pypi/+f/7ab/159b063c52a03/regex-2024.11.6.tar.gz", hash = "sha256:7ab159b063c52a0333c884e4679f8d7a85112ee3078fe3d9004b2dd875585519" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/547/8c6962ad548b5/regex-2024.11.6-cp311-cp311-macosx_10_9_universal2.whl", hash = "sha256:5478c6962ad548b54a591778e93cd7c456a7a29f8eca9c49e4f9a806dcc5d638" },
    { url = "http://***************:3141/zyx/pypi/+f/2c8/9a8cc122b25ce/regex-2024.11.6-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:2c89a8cc122b25ce6945f0423dc1352cb9593c68abd19223eebbd4e56612c5b7" },
    { url = "http://***************:3141/zyx/pypi/+f/94d/87b689cdd8319/regex-2024.11.6-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:94d87b689cdd831934fa3ce16cc15cd65748e6d689f5d2b8f4f4df2065c9fa20" },
    { url = "http://***************:3141/zyx/pypi/+f/106/2b39a0a2b75a9/regex-2024.11.6-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:1062b39a0a2b75a9c694f7a08e7183a80c63c0d62b301418ffd9c35f55aaa114" },
    { url = "http://***************:3141/zyx/pypi/+f/167/ed4852351d8a7/regex-2024.11.6-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:167ed4852351d8a750da48712c3930b031f6efdaa0f22fa1933716bfcd6bf4a3" },
    { url = "http://***************:3141/zyx/pypi/+f/2d5/48dafee61f06e/regex-2024.11.6-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:2d548dafee61f06ebdb584080621f3e0c23fff312f0de1afc776e2a2ba99a74f" },
    { url = "http://***************:3141/zyx/pypi/+f/f2a/19f302cd1ce5d/regex-2024.11.6-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:f2a19f302cd1ce5dd01a9099aaa19cae6173306d1302a43b627f62e21cf18ac0" },
    { url = "http://***************:3141/zyx/pypi/+f/bec/9931dfb61ddd8/regex-2024.11.6-cp311-cp311-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:bec9931dfb61ddd8ef2ebc05646293812cb6b16b60cf7c9511a832b6f1854b55" },
    { url = "http://***************:3141/zyx/pypi/+f/971/4398225f299aa/regex-2024.11.6-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:9714398225f299aa85267fd222f7142fcb5c769e73d7733344efc46f2ef5cf89" },
    { url = "http://***************:3141/zyx/pypi/+f/202/eb32e89f60fc1/regex-2024.11.6-cp311-cp311-musllinux_1_2_i686.whl", hash = "sha256:202eb32e89f60fc147a41e55cb086db2a3f8cb82f9a9a88440dcfc5d37faae8d" },
    { url = "http://***************:3141/zyx/pypi/+f/418/1b814e56078e9/regex-2024.11.6-cp311-cp311-musllinux_1_2_ppc64le.whl", hash = "sha256:4181b814e56078e9b00427ca358ec44333765f5ca1b45597ec7446d3a1ef6e34" },
    { url = "http://***************:3141/zyx/pypi/+f/068/376da5a7e4da5/regex-2024.11.6-cp311-cp311-musllinux_1_2_s390x.whl", hash = "sha256:068376da5a7e4da51968ce4c122a7cd31afaaec4fccc7856c92f63876e57b51d" },
    { url = "http://***************:3141/zyx/pypi/+f/ac1/0f2c4184420d8/regex-2024.11.6-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:ac10f2c4184420d881a3475fb2c6f4d95d53a8d50209a2500723d831036f7c45" },
    { url = "http://***************:3141/zyx/pypi/+f/c36/f9b6f5f8649bb/regex-2024.11.6-cp311-cp311-win32.whl", hash = "sha256:c36f9b6f5f8649bb251a5f3f66564438977b7ef8386a52460ae77e6070d309d9" },
    { url = "http://***************:3141/zyx/pypi/+f/02e/28184be537f0e/regex-2024.11.6-cp311-cp311-win_amd64.whl", hash = "sha256:02e28184be537f0e75c1f9b2f8847dc51e08e6e171c6bde130b2687e0c33cf60" },
    { url = "http://***************:3141/zyx/pypi/+f/52f/b28f528778f18/regex-2024.11.6-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:52fb28f528778f184f870b7cf8f225f5eef0a8f6e3778529bdd40c7b3920796a" },
    { url = "http://***************:3141/zyx/pypi/+f/fdd/6028445d2460f/regex-2024.11.6-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:fdd6028445d2460f33136c55eeb1f601ab06d74cb3347132e1c24250187500d9" },
    { url = "http://***************:3141/zyx/pypi/+f/805/e6b60c54bf766/regex-2024.11.6-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:805e6b60c54bf766b251e94526ebad60b7de0c70f70a4e6210ee2891acb70bf2" },
    { url = "http://***************:3141/zyx/pypi/+f/b85/c2530be953a89/regex-2024.11.6-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:b85c2530be953a890eaffde05485238f07029600e8f098cdf1848d414a8b45e4" },
    { url = "http://***************:3141/zyx/pypi/+f/bb2/6437975da7dc3/regex-2024.11.6-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:bb26437975da7dc36b7efad18aa9dd4ea569d2357ae6b783bf1118dabd9ea577" },
    { url = "http://***************:3141/zyx/pypi/+f/abf/a5080c374a76a/regex-2024.11.6-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:abfa5080c374a76a251ba60683242bc17eeb2c9818d0d30117b4486be10c59d3" },
    { url = "http://***************:3141/zyx/pypi/+f/70b/7fa6606c2881c/regex-2024.11.6-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:70b7fa6606c2881c1db9479b0eaa11ed5dfa11c8d60a474ff0e095099f39d98e" },
    { url = "http://***************:3141/zyx/pypi/+f/0c3/2f75920cf99fe/regex-2024.11.6-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:0c32f75920cf99fe6b6c539c399a4a128452eaf1af27f39bce8909c9a3fd8cbe" },
    { url = "http://***************:3141/zyx/pypi/+f/982/e6d21414e78e1/regex-2024.11.6-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:982e6d21414e78e1f51cf595d7f321dcd14de1f2881c5dc6a6e23bbbbd68435e" },
    { url = "http://***************:3141/zyx/pypi/+f/a7c/2155f790e2fb4/regex-2024.11.6-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:a7c2155f790e2fb448faed6dd241386719802296ec588a8b9051c1f5c481bc29" },
    { url = "http://***************:3141/zyx/pypi/+f/149/f5008d286636e/regex-2024.11.6-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:149f5008d286636e48cd0b1dd65018548944e495b0265b45e1bffecce1ef7f39" },
    { url = "http://***************:3141/zyx/pypi/+f/e53/64a4502efca09/regex-2024.11.6-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:e5364a4502efca094731680e80009632ad6624084aff9a23ce8c8c6820de3e51" },
    { url = "http://***************:3141/zyx/pypi/+f/0a8/6e7eeca091c09/regex-2024.11.6-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:0a86e7eeca091c09e021db8eb72d54751e527fa47b8d5787caf96d9831bd02ad" },
    { url = "http://***************:3141/zyx/pypi/+f/32f/9a4c643baad4e/regex-2024.11.6-cp312-cp312-win32.whl", hash = "sha256:32f9a4c643baad4efa81d549c2aadefaeba12249b2adc5af541759237eee1c54" },
    { url = "http://***************:3141/zyx/pypi/+f/a93/c194e2df18f7d/regex-2024.11.6-cp312-cp312-win_amd64.whl", hash = "sha256:a93c194e2df18f7d264092dc8539b8ffb86b45b899ab976aa15d48214138e81b" },
    { url = "http://***************:3141/zyx/pypi/+f/a6b/a92c0bcdf96cb/regex-2024.11.6-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:a6ba92c0bcdf96cbf43a12c717eae4bc98325ca3730f6b130ffa2e3c3c723d84" },
    { url = "http://***************:3141/zyx/pypi/+f/525/eab0b789891ac/regex-2024.11.6-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:525eab0b789891ac3be914d36893bdf972d483fe66551f79d3e27146191a37d4" },
    { url = "http://***************:3141/zyx/pypi/+f/086/a27a0b4ca2279/regex-2024.11.6-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:086a27a0b4ca227941700e0b31425e7a28ef1ae8e5e05a33826e17e47fbfdba0" },
    { url = "http://***************:3141/zyx/pypi/+f/bde/01f35767c4a78/regex-2024.11.6-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:bde01f35767c4a7899b7eb6e823b125a64de314a8ee9791367c9a34d56af18d0" },
    { url = "http://***************:3141/zyx/pypi/+f/b58/3904576650166/regex-2024.11.6-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:b583904576650166b3d920d2bcce13971f6f9e9a396c673187f49811b2769dc7" },
    { url = "http://***************:3141/zyx/pypi/+f/1c4/de13f06a0d54f/regex-2024.11.6-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:1c4de13f06a0d54fa0d5ab1b7138bfa0d883220965a29616e3ea61b35d5f5fc7" },
    { url = "http://***************:3141/zyx/pypi/+f/3cd/e6e9f2580eb16/regex-2024.11.6-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:3cde6e9f2580eb1665965ce9bf17ff4952f34f5b126beb509fee8f4e994f143c" },
    { url = "http://***************:3141/zyx/pypi/+f/0d7/f453dca13f40a/regex-2024.11.6-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:0d7f453dca13f40a02b79636a339c5b62b670141e63efd511d3f8f73fba162b3" },
    { url = "http://***************:3141/zyx/pypi/+f/59d/fe1ed21aea057/regex-2024.11.6-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:59dfe1ed21aea057a65c6b586afd2a945de04fc7db3de0a6e3ed5397ad491b07" },
    { url = "http://***************:3141/zyx/pypi/+f/b97/c1e0bd37c5cd7/regex-2024.11.6-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:b97c1e0bd37c5cd7902e65f410779d39eeda155800b65fc4d04cc432efa9bc6e" },
    { url = "http://***************:3141/zyx/pypi/+f/f9d/1e379028e0fc2/regex-2024.11.6-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:f9d1e379028e0fc2ae3654bac3cbbef81bf3fd571272a42d56c24007979bafb6" },
    { url = "http://***************:3141/zyx/pypi/+f/132/91b39131e2d00/regex-2024.11.6-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:13291b39131e2d002a7940fb176e120bec5145f3aeb7621be6534e46251912c4" },
    { url = "http://***************:3141/zyx/pypi/+f/4f5/1f88c126370dc/regex-2024.11.6-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:4f51f88c126370dcec4908576c5a627220da6c09d0bff31cfa89f2523843316d" },
    { url = "http://***************:3141/zyx/pypi/+f/63b/13cfd72e96011/regex-2024.11.6-cp313-cp313-win32.whl", hash = "sha256:63b13cfd72e9601125027202cad74995ab26921d8cd935c25f09c630436348ff" },
    { url = "http://***************:3141/zyx/pypi/+f/2b3/361af3198667e/regex-2024.11.6-cp313-cp313-win_amd64.whl", hash = "sha256:2b3361af3198667e99927da8b84c1b010752fa4b1115ee30beaa332cabc3ef1a" },
]

[[package]]
name = "scipy"
version = "1.16.1"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
dependencies = [
    { name = "numpy" },
]
sdist = { url = "http://***************:3141/zyx/pypi/+f/44c/76f9e8b6e8e48/scipy-1.16.1.tar.gz", hash = "sha256:44c76f9e8b6e8e488a586190ab38016e4ed2f8a038af7cd3defa903c0a2238b3" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/c03/3fa32bab91dc9/scipy-1.16.1-cp311-cp311-macosx_10_14_x86_64.whl", hash = "sha256:c033fa32bab91dc98ca59d0cf23bb876454e2bb02cbe592d5023138778f70030" },
    { url = "http://***************:3141/zyx/pypi/+f/6e5/c2f74e5df3347/scipy-1.16.1-cp311-cp311-macosx_12_0_arm64.whl", hash = "sha256:6e5c2f74e5df33479b5cd4e97a9104c511518fbd979aa9b8f6aec18b2e9ecae7" },
    { url = "http://***************:3141/zyx/pypi/+f/0a5/5ffe0ba0f5966/scipy-1.16.1-cp311-cp311-macosx_14_0_arm64.whl", hash = "sha256:0a55ffe0ba0f59666e90951971a884d1ff6f4ec3275a48f472cfb64175570f77" },
    { url = "http://***************:3141/zyx/pypi/+f/f8a/5d6cd147acecc/scipy-1.16.1-cp311-cp311-macosx_14_0_x86_64.whl", hash = "sha256:f8a5d6cd147acecc2603fbd382fed6c46f474cccfcf69ea32582e033fb54dcfe" },
    { url = "http://***************:3141/zyx/pypi/+f/cb1/8899127278058/scipy-1.16.1-cp311-cp311-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:cb18899127278058bcc09e7b9966d41a5a43740b5bb8dcba401bd983f82e885b" },
    { url = "http://***************:3141/zyx/pypi/+f/adc/cd93a2fa937a2/scipy-1.16.1-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:adccd93a2fa937a27aae826d33e3bfa5edf9aa672376a4852d23a7cd67a2e5b7" },
    { url = "http://***************:3141/zyx/pypi/+f/18a/ca1646a29ee9a/scipy-1.16.1-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:18aca1646a29ee9a0625a1be5637fa798d4d81fdf426481f06d69af828f16958" },
    { url = "http://***************:3141/zyx/pypi/+f/d85/495cef541729a/scipy-1.16.1-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:d85495cef541729a70cdddbbf3e6b903421bc1af3e8e3a9a72a06751f33b7c39" },
    { url = "http://***************:3141/zyx/pypi/+f/226/652fca8530081/scipy-1.16.1-cp311-cp311-win_amd64.whl", hash = "sha256:226652fca853008119c03a8ce71ffe1b3f6d2844cc1686e8f9806edafae68596" },
    { url = "http://***************:3141/zyx/pypi/+f/81b/433bbeaf35728/scipy-1.16.1-cp312-cp312-macosx_10_14_x86_64.whl", hash = "sha256:81b433bbeaf35728dad619afc002db9b189e45eebe2cd676effe1fb93fef2b9c" },
    { url = "http://***************:3141/zyx/pypi/+f/886/cc81fdb4c6903/scipy-1.16.1-cp312-cp312-macosx_12_0_arm64.whl", hash = "sha256:886cc81fdb4c6903a3bb0464047c25a6d1016fef77bb97949817d0c0d79f9e04" },
    { url = "http://***************:3141/zyx/pypi/+f/152/40c3aac087a52/scipy-1.16.1-cp312-cp312-macosx_14_0_arm64.whl", hash = "sha256:15240c3aac087a522b4eaedb09f0ad061753c5eebf1ea430859e5bf8640d5919" },
    { url = "http://***************:3141/zyx/pypi/+f/65f/81a25805f3659/scipy-1.16.1-cp312-cp312-macosx_14_0_x86_64.whl", hash = "sha256:65f81a25805f3659b48126b5053d9e823d3215e4a63730b5e1671852a1705921" },
    { url = "http://***************:3141/zyx/pypi/+f/6c6/2eea7f607f122/scipy-1.16.1-cp312-cp312-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:6c62eea7f607f122069b9bad3f99489ddca1a5173bef8a0c75555d7488b6f725" },
    { url = "http://***************:3141/zyx/pypi/+f/f96/5bbf3235b01c7/scipy-1.16.1-cp312-cp312-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:f965bbf3235b01c776115ab18f092a95aa74c271a52577bcb0563e85738fd618" },
    { url = "http://***************:3141/zyx/pypi/+f/f00/6e323874ffd0b/scipy-1.16.1-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:f006e323874ffd0b0b816d8c6a8e7f9a73d55ab3b8c3f72b752b226d0e3ac83d" },
    { url = "http://***************:3141/zyx/pypi/+f/e8f/d15fc5085ab4c/scipy-1.16.1-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:e8fd15fc5085ab4cca74cb91fe0a4263b1f32e4420761ddae531ad60934c2119" },
    { url = "http://***************:3141/zyx/pypi/+f/f7b/8013c6c066609/scipy-1.16.1-cp312-cp312-win_amd64.whl", hash = "sha256:f7b8013c6c066609577d910d1a2a077021727af07b6fab0ee22c2f901f22352a" },
    { url = "http://***************:3141/zyx/pypi/+f/545/1606823a5e73d/scipy-1.16.1-cp313-cp313-macosx_10_14_x86_64.whl", hash = "sha256:5451606823a5e73dfa621a89948096c6528e2896e40b39248295d3a0138d594f" },
    { url = "http://***************:3141/zyx/pypi/+f/897/28678c5ca5abd/scipy-1.16.1-cp313-cp313-macosx_12_0_arm64.whl", hash = "sha256:89728678c5ca5abd610aee148c199ac1afb16e19844401ca97d43dc548a354eb" },
    { url = "http://***************:3141/zyx/pypi/+f/e75/6d688cb03fd07/scipy-1.16.1-cp313-cp313-macosx_14_0_arm64.whl", hash = "sha256:e756d688cb03fd07de0fffad475649b03cb89bee696c98ce508b17c11a03f95c" },
    { url = "http://***************:3141/zyx/pypi/+f/5aa/2687b9935da3e/scipy-1.16.1-cp313-cp313-macosx_14_0_x86_64.whl", hash = "sha256:5aa2687b9935da3ed89c5dbed5234576589dd28d0bf7cd237501ccfbdf1ad608" },
    { url = "http://***************:3141/zyx/pypi/+f/085/1f6a1e537fe93/scipy-1.16.1-cp313-cp313-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:0851f6a1e537fe9399f35986897e395a1aa61c574b178c0d456be5b1a0f5ca1f" },
    { url = "http://***************:3141/zyx/pypi/+f/fed/c2cbd1baed374/scipy-1.16.1-cp313-cp313-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:fedc2cbd1baed37474b1924c331b97bdff611d762c196fac1a9b71e67b813b1b" },
    { url = "http://***************:3141/zyx/pypi/+f/2ef/500e72f9623a6/scipy-1.16.1-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:2ef500e72f9623a6735769e4b93e9dcb158d40752cdbb077f305487e3e2d1f45" },
    { url = "http://***************:3141/zyx/pypi/+f/978/d8311674b05a8/scipy-1.16.1-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:978d8311674b05a8f7ff2ea6c6bce5d8b45a0cb09d4c5793e0318f448613ea65" },
    { url = "http://***************:3141/zyx/pypi/+f/819/29ed0fa7a5713/scipy-1.16.1-cp313-cp313-win_amd64.whl", hash = "sha256:81929ed0fa7a5713fcdd8b2e6f73697d3b4c4816d090dd34ff937c20fa90e8ab" },
    { url = "http://***************:3141/zyx/pypi/+f/bcc/12db731858abd/scipy-1.16.1-cp313-cp313t-macosx_10_14_x86_64.whl", hash = "sha256:bcc12db731858abda693cecdb3bdc9e6d4bd200213f49d224fe22df82687bdd6" },
    { url = "http://***************:3141/zyx/pypi/+f/744/d977daa4becb9/scipy-1.16.1-cp313-cp313t-macosx_12_0_arm64.whl", hash = "sha256:744d977daa4becb9fc59135e75c069f8d301a87d64f88f1e602a9ecf51e77b27" },
    { url = "http://***************:3141/zyx/pypi/+f/dc5/4f76ac18073bc/scipy-1.16.1-cp313-cp313t-macosx_14_0_arm64.whl", hash = "sha256:dc54f76ac18073bcecffb98d93f03ed6b81a92ef91b5d3b135dcc81d55a724c7" },
    { url = "http://***************:3141/zyx/pypi/+f/367/d567ee9fc1e9e/scipy-1.16.1-cp313-cp313t-macosx_14_0_x86_64.whl", hash = "sha256:367d567ee9fc1e9e2047d31f39d9d6a7a04e0710c86e701e053f237d14a9b4f6" },
    { url = "http://***************:3141/zyx/pypi/+f/4cf/5785e44e19dcd/scipy-1.16.1-cp313-cp313t-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:4cf5785e44e19dcd32a0e4807555e1e9a9b8d475c6afff3d21c3c543a6aa84f4" },
    { url = "http://***************:3141/zyx/pypi/+f/3d0/b80fb26d3e13a/scipy-1.16.1-cp313-cp313t-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:3d0b80fb26d3e13a794c71d4b837e2a589d839fd574a6bbb4ee1288c213ad4a3" },
    { url = "http://***************:3141/zyx/pypi/+f/850/3517c44c18d10/scipy-1.16.1-cp313-cp313t-musllinux_1_2_aarch64.whl", hash = "sha256:8503517c44c18d1030d666cb70aaac1cc8913608816e06742498833b128488b7" },
    { url = "http://***************:3141/zyx/pypi/+f/30c/c4bb81c41831e/scipy-1.16.1-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:30cc4bb81c41831ecfd6dc450baf48ffd80ef5aed0f5cf3ea775740e80f16ecc" },
    { url = "http://***************:3141/zyx/pypi/+f/c24/fa02f7ed23ae5/scipy-1.16.1-cp313-cp313t-win_amd64.whl", hash = "sha256:c24fa02f7ed23ae514460a22c57eca8f530dbfa50b1cfdbf4f37c05b5309cc39" },
    { url = "http://***************:3141/zyx/pypi/+f/796/a5a9ad36fa3a7/scipy-1.16.1-cp314-cp314-macosx_10_14_x86_64.whl", hash = "sha256:796a5a9ad36fa3a782375db8f4241ab02a091308eb079746bc0f874c9b998318" },
    { url = "http://***************:3141/zyx/pypi/+f/3ea/0733a2ff73fd6/scipy-1.16.1-cp314-cp314-macosx_12_0_arm64.whl", hash = "sha256:3ea0733a2ff73fd6fdc5fecca54ee9b459f4d74f00b99aced7d9a3adb43fb1cc" },
    { url = "http://***************:3141/zyx/pypi/+f/857/64fb15a2ad994/scipy-1.16.1-cp314-cp314-macosx_14_0_arm64.whl", hash = "sha256:85764fb15a2ad994e708258bb4ed8290d1305c62a4e1ef07c414356a24fcfbf8" },
    { url = "http://***************:3141/zyx/pypi/+f/ca6/6d980469cb623/scipy-1.16.1-cp314-cp314-macosx_14_0_x86_64.whl", hash = "sha256:ca66d980469cb623b1759bdd6e9fd97d4e33a9fad5b33771ced24d0cb24df67e" },
    { url = "http://***************:3141/zyx/pypi/+f/e7c/c1ffcc230f568/scipy-1.16.1-cp314-cp314-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:e7cc1ffcc230f568549fc56670bcf3df1884c30bd652c5da8138199c8c76dae0" },
    { url = "http://***************:3141/zyx/pypi/+f/3dd/fb1e8d0b540cb/scipy-1.16.1-cp314-cp314-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:3ddfb1e8d0b540cb4ee9c53fc3dea3186f97711248fb94b4142a1b27178d8b4b" },
    { url = "http://***************:3141/zyx/pypi/+f/4dc/0e7be79e95d8b/scipy-1.16.1-cp314-cp314-musllinux_1_2_aarch64.whl", hash = "sha256:4dc0e7be79e95d8ba3435d193e0d8ce372f47f774cffd882f88ea4e1e1ddc731" },
    { url = "http://***************:3141/zyx/pypi/+f/f23/634f9e5adb51b/scipy-1.16.1-cp314-cp314-musllinux_1_2_x86_64.whl", hash = "sha256:f23634f9e5adb51b2a77766dac217063e764337fbc816aa8ad9aaebcd4397fd3" },
    { url = "http://***************:3141/zyx/pypi/+f/57d/75524cb1c5a37/scipy-1.16.1-cp314-cp314-win_amd64.whl", hash = "sha256:57d75524cb1c5a374958a2eae3d84e1929bb971204cc9d52213fb8589183fc19" },
    { url = "http://***************:3141/zyx/pypi/+f/d8d/a7c3dd67bcd93/scipy-1.16.1-cp314-cp314t-macosx_10_14_x86_64.whl", hash = "sha256:d8da7c3dd67bcd93f15618938f43ed0995982eb38973023d46d4646c4283ad65" },
    { url = "http://***************:3141/zyx/pypi/+f/cc1/d2f2fd48ba1e0/scipy-1.16.1-cp314-cp314t-macosx_12_0_arm64.whl", hash = "sha256:cc1d2f2fd48ba1e0620554fe5bc44d3e8f5d4185c8c109c7fbdf5af2792cfad2" },
    { url = "http://***************:3141/zyx/pypi/+f/21a/611ced9275cb8/scipy-1.16.1-cp314-cp314t-macosx_14_0_arm64.whl", hash = "sha256:21a611ced9275cb861bacadbada0b8c0623bc00b05b09eb97f23b370fc2ae56d" },
    { url = "http://***************:3141/zyx/pypi/+f/8df/bb25dffc4c3dd/scipy-1.16.1-cp314-cp314t-macosx_14_0_x86_64.whl", hash = "sha256:8dfbb25dffc4c3dd9371d8ab456ca81beeaf6f9e1c2119f179392f0dc1ab7695" },
    { url = "http://***************:3141/zyx/pypi/+f/f0e/bb7204f063fad/scipy-1.16.1-cp314-cp314t-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:f0ebb7204f063fad87fc0a0e4ff4a2ff40b2a226e4ba1b7e34bf4b79bf97cd86" },
    { url = "http://***************:3141/zyx/pypi/+f/f1b/9e5962656f273/scipy-1.16.1-cp314-cp314t-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:f1b9e5962656f2734c2b285a8745358ecb4e4efbadd00208c80a389227ec61ff" },
    { url = "http://***************:3141/zyx/pypi/+f/5e1/a106f8c023d57/scipy-1.16.1-cp314-cp314t-musllinux_1_2_aarch64.whl", hash = "sha256:5e1a106f8c023d57a2a903e771228bf5c5b27b5d692088f457acacd3b54511e4" },
    { url = "http://***************:3141/zyx/pypi/+f/709/559a1db68a9ab/scipy-1.16.1-cp314-cp314t-musllinux_1_2_x86_64.whl", hash = "sha256:709559a1db68a9abc3b2c8672c4badf1614f3b440b3ab326d86a5c0491eafae3" },
    { url = "http://***************:3141/zyx/pypi/+f/c0c/804d60492a0aa/scipy-1.16.1-cp314-cp314t-win_amd64.whl", hash = "sha256:c0c804d60492a0aad7f5b2bb1862f4548b990049e27e828391ff2bf6f7199998" },
]

[[package]]
name = "sipyco"
version = "1.9.3"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
sdist = { url = "http://***************:3141/zyx/devpi/+f/991/e93fc8f25099c/sipyco-1.9.3.tar.gz", hash = "sha256:991e93fc8f25099c2596e3094de4c2fb9bdf3a82fbc7fc97ee9659a1fd013798" }
wheels = [
    { url = "http://***************:3141/zyx/devpi/+f/f2a/a8eb34fb962ef/sipyco-1.9.3-py3-none-any.whl", hash = "sha256:f2aa8eb34fb962efee843130c8e43d8032003a06a5548fb271c30dba57e60374" },
]

[[package]]
name = "six"
version = "1.17.0"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
sdist = { url = "http://***************:3141/zyx/pypi/+f/ff7/0335d468e7eb6/six-1.17.0.tar.gz", hash = "sha256:ff70335d468e7eb6ec65b95b99d3a2836546063f63acc5171de367e834932a81" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/472/1f391ed90541f/six-1.17.0-py2.py3-none-any.whl", hash = "sha256:4721f391ed90541fddacab5acf947aa0d3dc7d27b2e1e8eda2be8970586c3274" },
]

[[package]]
name = "wcwidth"
version = "0.2.13"
source = { registry = "http://***************:3141/zyx/devpi/+simple/" }
sdist = { url = "http://***************:3141/zyx/pypi/+f/72e/a0c06399eb286/wcwidth-0.2.13.tar.gz", hash = "sha256:72ea0c06399eb286d978fdedb6923a9eb47e1c486ce63e9b4e64fc18303972b5" }
wheels = [
    { url = "http://***************:3141/zyx/pypi/+f/3da/69048e4540d84/wcwidth-0.2.13-py2.py3-none-any.whl", hash = "sha256:3da69048e4540d84af32131829ff948f1e022c1c6bdb8d6102117aac784f6859" },
]
